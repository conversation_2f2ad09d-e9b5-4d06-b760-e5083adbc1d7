'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Send, Copy, Eye, Trash2, RefreshCw } from 'lucide-react';

interface PreRegistration {
  id: string;
  studentName: string;
  nationalId?: string;
  dateOfBirth?: string;
  gender?: string;
  examLevel: string;
  academicYear: string;
  status: string;
  verifiedBySchool: boolean;
  verifiedBy?: string;
  verificationDate?: string;
  invitationCode?: string;
  invitationExpiry?: string;
  createdAt: string;
}

interface Invitation {
  id: string;
  code: string;
  studentName: string;
  examLevel: string;
  expiresAt: string;
  isUsed: boolean;
  usedAt?: string;
  status: 'active' | 'used' | 'expired';
  createdAt: string;
}

interface SchoolPreRegistrationProps {
  schoolId: string;
  schoolName: string;
  userRole: string; // 'admin', 'teacher', etc.
}

export default function SchoolPreRegistration({ schoolId, schoolName, userRole }: SchoolPreRegistrationProps) {
  const [preRegistrations, setPreRegistrations] = useState<PreRegistration[]>([]);
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'pre-registrations' | 'invitations'>('pre-registrations');

  // Form state for creating pre-registration
  const [formData, setFormData] = useState({
    studentName: '',
    nationalId: '',
    dateOfBirth: '',
    gender: '',
    examLevel: '',
    academicYear: '2025'
  });

  // Load data on component mount
  useEffect(() => {
    loadPreRegistrations();
    loadInvitations();
  }, [schoolId]);

  const loadPreRegistrations = async () => {
    try {
      const response = await fetch(`/api/schools/pre-registration?schoolId=${schoolId}`);
      const data = await response.json();
      
      if (data.success) {
        setPreRegistrations(data.data);
      }
    } catch (error) {
      console.error('Error loading pre-registrations:', error);
    }
  };

  const loadInvitations = async () => {
    try {
      const response = await fetch(`/api/schools/invitations?schoolId=${schoolId}`);
      const data = await response.json();
      
      if (data.success) {
        setInvitations(data.data);
      }
    } catch (error) {
      console.error('Error loading invitations:', error);
    }
  };

  const handleCreatePreRegistration = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/schools/pre-registration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          schoolId,
          verifiedBy: 'Current User' // In real app, get from auth context
        })
      });

      const data = await response.json();

      if (data.success) {
        setFormData({
          studentName: '',
          nationalId: '',
          dateOfBirth: '',
          gender: '',
          examLevel: '',
          academicYear: '2025'
        });
        setShowCreateForm(false);
        loadPreRegistrations();
        alert('Pre-registration created successfully!');
      } else {
        alert('Error: ' + data.message);
      }
    } catch (error) {
      console.error('Error creating pre-registration:', error);
      alert('Error creating pre-registration');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateInvitation = async (preRegId: string) => {
    setLoading(true);

    try {
      const response = await fetch('/api/schools/invitations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          preRegistrationId: preRegId,
          createdBy: 'Current User' // In real app, get from auth context
        })
      });

      const data = await response.json();

      if (data.success) {
        loadPreRegistrations();
        loadInvitations();
        alert(`Invitation generated successfully!\nCode: ${data.data.code}\nExpires: ${new Date(data.data.expiresAt).toLocaleDateString()}`);
      } else {
        alert('Error: ' + data.message);
      }
    } catch (error) {
      console.error('Error generating invitation:', error);
      alert('Error generating invitation');
    } finally {
      setLoading(false);
    }
  };

  const copyInvitationCode = (code: string) => {
    navigator.clipboard.writeText(code);
    alert('Invitation code copied to clipboard!');
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      pending: 'bg-yellow-100 text-yellow-800',
      verified: 'bg-blue-100 text-blue-800',
      invitation_sent: 'bg-green-100 text-green-800',
      completed: 'bg-gray-100 text-gray-800',
      expired: 'bg-red-100 text-red-800',
      active: 'bg-green-100 text-green-800',
      used: 'bg-gray-100 text-gray-800'
    };

    return (
      <Badge className={statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Student Pre-Registration</h2>
          <p className="text-gray-600">{schoolName}</p>
        </div>
        
        <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Student
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create Pre-Registration</DialogTitle>
              <DialogDescription>
                Add a student for pre-registration verification
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleCreatePreRegistration} className="space-y-4">
              <div>
                <Label htmlFor="studentName">Student Name *</Label>
                <Input
                  id="studentName"
                  value={formData.studentName}
                  onChange={(e) => setFormData({...formData, studentName: e.target.value})}
                  required
                />
              </div>

              <div>
                <Label htmlFor="examLevel">Exam Level *</Label>
                <Select value={formData.examLevel} onValueChange={(value) => setFormData({...formData, examLevel: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select exam level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="O Level">O Level</SelectItem>
                    <SelectItem value="A Level">A Level</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="nationalId">National ID</Label>
                <Input
                  id="nationalId"
                  value={formData.nationalId}
                  onChange={(e) => setFormData({...formData, nationalId: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="dateOfBirth">Date of Birth</Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => setFormData({...formData, dateOfBirth: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="gender">Gender</Label>
                <Select value={formData.gender} onValueChange={(value) => setFormData({...formData, gender: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Male">Male</SelectItem>
                    <SelectItem value="Female">Female</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowCreateForm(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Creating...' : 'Create'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          className={`px-4 py-2 rounded-md transition-colors ${
            selectedTab === 'pre-registrations' 
              ? 'bg-white shadow-sm text-blue-600' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
          onClick={() => setSelectedTab('pre-registrations')}
        >
          Pre-Registrations ({preRegistrations.length})
        </button>
        <button
          className={`px-4 py-2 rounded-md transition-colors ${
            selectedTab === 'invitations' 
              ? 'bg-white shadow-sm text-blue-600' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
          onClick={() => setSelectedTab('invitations')}
        >
          Invitations ({invitations.length})
        </button>
      </div>

      {/* Content based on selected tab */}
      {selectedTab === 'pre-registrations' && (
        <div className="grid gap-4">
          {preRegistrations.map((preReg) => (
            <Card key={preReg.id}>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold">{preReg.studentName}</h3>
                      {getStatusBadge(preReg.status)}
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Exam Level: {preReg.examLevel}</p>
                      {preReg.nationalId && <p>National ID: {preReg.nationalId}</p>}
                      {preReg.dateOfBirth && <p>Date of Birth: {preReg.dateOfBirth}</p>}
                      <p>Created: {new Date(preReg.createdAt).toLocaleDateString()}</p>
                      {preReg.verifiedBy && <p>Verified by: {preReg.verifiedBy}</p>}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    {preReg.status === 'verified' && !preReg.invitationCode && (
                      <Button
                        size="sm"
                        onClick={() => handleGenerateInvitation(preReg.id)}
                        disabled={loading}
                      >
                        <Send className="w-4 h-4 mr-1" />
                        Generate Invitation
                      </Button>
                    )}
                    
                    {preReg.invitationCode && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyInvitationCode(preReg.invitationCode!)}
                      >
                        <Copy className="w-4 h-4 mr-1" />
                        Copy Code
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {preRegistrations.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center text-gray-500">
                No pre-registrations found. Click "Add Student" to create one.
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {selectedTab === 'invitations' && (
        <div className="grid gap-4">
          {invitations.map((invitation) => (
            <Card key={invitation.id}>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold">{invitation.studentName}</h3>
                      {getStatusBadge(invitation.status)}
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Exam Level: {invitation.examLevel}</p>
                      <p>Code: <code className="bg-gray-100 px-2 py-1 rounded">{invitation.code}</code></p>
                      <p>Expires: {new Date(invitation.expiresAt).toLocaleDateString()}</p>
                      {invitation.usedAt && <p>Used: {new Date(invitation.usedAt).toLocaleDateString()}</p>}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyInvitationCode(invitation.code)}
                    >
                      <Copy className="w-4 h-4 mr-1" />
                      Copy
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {invitations.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center text-gray-500">
                No invitations found. Generate invitations from verified pre-registrations.
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
