import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/postgresDb';

// GET /api/professional-status - Check professional application status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    const applicantType = searchParams.get('applicantType');

    if (!email || !applicantType) {
      return NextResponse.json(
        { success: false, message: 'Email and applicant type are required' },
        { status: 400 }
      );
    }

    if (!['teacher', 'examiner'].includes(applicantType)) {
      return NextResponse.json(
        { success: false, message: 'Invalid applicant type' },
        { status: 400 }
      );
    }

    // Find the most recent application
    const application = await prisma.professionalRegistration.findFirst({
      where: {
        email: email.toLowerCase(),
        applicantType: applicantType
      },
      orderBy: { createdAt: 'desc' },
      include: {
        // Include related verification records if needed
      }
    });

    if (!application) {
      return NextResponse.json(
        { 
          success: false, 
          message: `No ${applicantType} application found for this email address.`,
          hasApplication: false
        },
        { status: 404 }
      );
    }

    // Get additional status information based on type
    let additionalInfo = {};

    if (applicantType === 'teacher') {
      // Get institution verification status
      const institutionVerification = await prisma.institutionVerification.findFirst({
        where: { registrationId: application.id },
        select: {
          status: true,
          verificationDate: true,
          verificationNotes: true,
          institutionName: true
        }
      });

      additionalInfo = {
        institutionVerification: institutionVerification
      };
    } else if (applicantType === 'examiner') {
      // Get examiner approval status
      const examinerApproval = await prisma.examinerApproval.findFirst({
        where: { registrationId: application.id },
        select: {
          status: true,
          approvalDate: true,
          approvalNumber: true,
          securityLevel: true,
          clearanceExpiry: true
        }
      });

      additionalInfo = {
        examinerApproval: examinerApproval
      };
    }

    // Prepare status response
    const statusResponse = {
      hasApplication: true,
      applicationId: application.id,
      status: application.status,
      submittedAt: application.submittedAt,
      reviewedAt: application.reviewedAt,
      approvedAt: application.approvedAt,
      rejectionReason: application.rejectionReason,
      accountCreated: application.accountCreated,
      accountId: application.accountId,
      ...additionalInfo
    };

    // Add status-specific messages
    let statusMessage = '';
    let nextSteps = [];

    switch (application.status) {
      case 'pending':
        statusMessage = applicantType === 'teacher' 
          ? 'Your teacher application is pending institutional verification.'
          : 'Your examiner application is pending government review.';
        nextSteps = applicantType === 'teacher'
          ? [
              'Your school will verify your employment and credentials',
              'Regional education office will review your qualifications',
              'You will receive email notification when approved'
            ]
          : [
              'Ministry of Education will review your qualifications',
              'Security background check will be conducted',
              'You will receive notification when clearance is complete'
            ];
        break;

      case 'under_review':
        statusMessage = applicantType === 'teacher'
          ? 'Your application is under review by education authorities.'
          : 'Your application is under review by the Ministry of Education.';
        nextSteps = [
          'Authorities are verifying your credentials',
          'Background checks are in progress',
          'Please wait for the review to complete'
        ];
        break;

      case 'approved':
        if (application.accountCreated) {
          statusMessage = 'Your application has been approved and your account is ready.';
          nextSteps = ['You can now log in to the system'];
        } else {
          statusMessage = 'Your application has been approved. Account creation is in progress.';
          nextSteps = [
            'Your account is being set up',
            'You will receive login credentials soon',
            'Contact support if you don\'t receive them within 24 hours'
          ];
        }
        break;

      case 'rejected':
        statusMessage = `Your ${applicantType} application has been rejected.`;
        nextSteps = [
          'Review the rejection reason below',
          'Contact support for clarification if needed',
          'You may reapply after addressing the issues'
        ];
        break;

      case 'suspended':
        statusMessage = `Your ${applicantType} certification has been suspended.`;
        nextSteps = [
          'Contact the education authorities immediately',
          'Provide any requested documentation',
          'Appeal the decision if appropriate'
        ];
        break;

      default:
        statusMessage = 'Application status is unclear.';
        nextSteps = ['Contact support for assistance'];
    }

    return NextResponse.json({
      success: true,
      data: {
        ...statusResponse,
        statusMessage,
        nextSteps,
        canLogin: application.accountCreated && application.status === 'approved',
        estimatedProcessingTime: applicantType === 'teacher' ? '5-10 business days' : '15-30 business days'
      },
      message: 'Application status retrieved successfully'
    });

  } catch (error) {
    console.error('Professional status check error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/professional-status - Update application status (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { applicationId, status, notes, adminId } = body;

    if (!applicationId || !status) {
      return NextResponse.json(
        { success: false, message: 'Application ID and status are required' },
        { status: 400 }
      );
    }

    // Validate status
    const validStatuses = ['pending', 'under_review', 'approved', 'rejected', 'suspended'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, message: 'Invalid status' },
        { status: 400 }
      );
    }

    // Update application status
    const updateData: any = { status };

    if (status === 'under_review') {
      updateData.reviewedAt = new Date();
      updateData.reviewedBy = adminId;
    } else if (status === 'approved') {
      updateData.approvedAt = new Date();
      updateData.approvedBy = adminId;
    } else if (status === 'rejected') {
      updateData.rejectionReason = notes;
    }

    const updatedApplication = await prisma.professionalRegistration.update({
      where: { id: applicationId },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: updatedApplication,
      message: 'Application status updated successfully'
    });

  } catch (error) {
    console.error('Update application status error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
