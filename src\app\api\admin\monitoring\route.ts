import { NextRequest, NextResponse } from 'next/server';
import { 
  MonitoringDashboard, 
  MetricsCollector, 
  ErrorTracker, 
  UserAnalytics, 
  HealthMonitor 
} from '@/lib/monitoring';
import { CacheManager, PerformanceMonitor, DatabaseOptimizer } from '@/lib/performance';
import { RedisManager } from '@/lib/redis';

// GET - Get monitoring dashboard data
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!isAdmin(token)) {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'overview';
    const timeRange = searchParams.get('timeRange') || '1h';

    // Calculate time range
    const now = new Date();
    const timeRanges = {
      '1h': new Date(now.getTime() - 60 * 60 * 1000),
      '24h': new Date(now.getTime() - 24 * 60 * 60 * 1000),
      '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    };

    const startTime = timeRanges[timeRange as keyof typeof timeRanges] || timeRanges['1h'];

    let data;

    switch (type) {
      case 'overview':
        data = await getSystemOverview(startTime, now);
        break;
      case 'performance':
        data = await getPerformanceMetrics(startTime, now);
        break;
      case 'errors':
        data = await getErrorAnalytics(startTime, now);
        break;
      case 'analytics':
        data = await getUserAnalytics(startTime, now);
        break;
      case 'health':
        data = await getSystemHealth();
        break;
      case 'cache':
        data = await getCacheMetrics();
        break;
      default:
        return NextResponse.json(
          { success: false, message: 'Invalid monitoring type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      data,
      metadata: {
        type,
        timeRange,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Monitoring API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Record custom metric or event
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, name, value, unit, tags, severity, context, event, properties, userId } = body;

    switch (type) {
      case 'metric':
        if (!name || value === undefined) {
          return NextResponse.json(
            { success: false, message: 'Metric name and value are required' },
            { status: 400 }
          );
        }
        MetricsCollector.record(name, value, unit || 'count', tags);
        break;

      case 'error':
        if (!name) {
          return NextResponse.json(
            { success: false, message: 'Error message is required' },
            { status: 400 }
          );
        }
        ErrorTracker.track(name, severity || 'medium', context);
        break;

      case 'event':
        if (!event) {
          return NextResponse.json(
            { success: false, message: 'Event name is required' },
            { status: 400 }
          );
        }
        UserAnalytics.track(event, properties, userId);
        break;

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid type. Must be metric, error, or event' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: `${type} recorded successfully`
    });

  } catch (error) {
    console.error('Monitoring POST error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to check admin permissions
function isAdmin(token: string): boolean {
  try {
    const tokenParts = token.split('-');
    if (tokenParts.length < 2) return false;
    
    const userType = tokenParts[0];
    return userType === 'admin';
  } catch {
    return false;
  }
}

// Get system overview data
async function getSystemOverview(startTime: Date, endTime: Date) {
  const [
    performanceMetrics,
    errorStats,
    userAnalytics,
    systemHealth,
    cacheStats
  ] = await Promise.all([
    getPerformanceMetrics(startTime, endTime),
    getErrorAnalytics(startTime, endTime),
    getUserAnalytics(startTime, endTime),
    getSystemHealth(),
    getCacheMetrics()
  ]);

  return {
    performance: performanceMetrics,
    errors: errorStats,
    analytics: userAnalytics,
    health: systemHealth,
    cache: cacheStats,
    summary: {
      totalRequests: performanceMetrics.apiRequests?.count || 0,
      averageResponseTime: performanceMetrics.apiResponseTime?.average || 0,
      errorRate: errorStats.errorRate || 0,
      activeUsers: userAnalytics.uniqueUsers || 0,
      systemUptime: calculateSystemUptime(systemHealth),
      cacheHitRate: cacheStats.hitRate || 0
    }
  };
}

// Get performance metrics
async function getPerformanceMetrics(startTime: Date, endTime: Date) {
  const timeRange = { start: startTime, end: endTime };

  return {
    apiResponseTime: MetricsCollector.getStats('api.response_time', timeRange),
    apiRequests: MetricsCollector.getStats('api.requests', timeRange),
    databaseQueryTime: MetricsCollector.getStats('database.query_time', timeRange),
    memoryUsage: MetricsCollector.getStats('memory.usage', timeRange),
    cpuUsage: MetricsCollector.getStats('cpu.usage', timeRange),
    performanceHistory: PerformanceMonitor.getStats('api.response_time', timeRange)
  };
}

// Get error analytics
async function getErrorAnalytics(startTime: Date, endTime: Date) {
  const timeRange = { start: startTime, end: endTime };
  const errorStats = ErrorTracker.getErrorStats(timeRange);
  const totalRequests = MetricsCollector.getStats('api.requests', timeRange).count;
  
  return {
    ...errorStats,
    errorRate: totalRequests > 0 ? errorStats.total / totalRequests : 0,
    recentErrors: ErrorTracker.getErrors(undefined, timeRange).slice(0, 10)
  };
}

// Get user analytics
async function getUserAnalytics(startTime: Date, endTime: Date) {
  const timeRange = { start: startTime, end: endTime };
  return UserAnalytics.getAnalytics(timeRange);
}

// Get system health
async function getSystemHealth() {
  const currentHealth = HealthMonitor.getCurrentHealth();
  
  // Add real-time health checks
  const [databaseHealth, redisHealth] = await Promise.all([
    DatabaseOptimizer.healthCheck(),
    RedisManager.healthCheck()
  ]);

  return {
    services: {
      ...currentHealth,
      database: {
        status: databaseHealth.healthy ? 'healthy' : 'unhealthy',
        responseTime: databaseHealth.responseTime,
        lastCheck: new Date(),
        error: databaseHealth.error
      },
      redis: {
        status: redisHealth.healthy ? 'healthy' : 'unhealthy',
        responseTime: redisHealth.responseTime,
        lastCheck: new Date(),
        error: redisHealth.error
      }
    },
    overall: calculateOverallHealth(currentHealth, databaseHealth, redisHealth)
  };
}

// Get cache metrics
async function getCacheMetrics() {
  const memoryCache = CacheManager.getStats();
  let redisStats = null;

  try {
    redisStats = await RedisManager.getStats();
  } catch (error) {
    // Redis not available
  }

  return {
    memory: {
      size: memoryCache.size,
      hitRate: memoryCache.hitRate,
      entries: memoryCache.entries.length
    },
    redis: redisStats ? {
      connected: redisStats.connected,
      memory: redisStats.memory,
      keys: redisStats.keys,
      hitRate: redisStats.hitRate
    } : null,
    hitRate: redisStats ? redisStats.hitRate : memoryCache.hitRate
  };
}

// Calculate system uptime
function calculateSystemUptime(healthData: any): number {
  const services = Object.values(healthData.services || {}) as any[];
  if (services.length === 0) return 1;

  const healthyServices = services.filter(service => service.status === 'healthy').length;
  return healthyServices / services.length;
}

// Calculate overall system health
function calculateOverallHealth(
  currentHealth: any,
  databaseHealth: any,
  redisHealth: any
): 'healthy' | 'degraded' | 'unhealthy' {
  const criticalServices = [databaseHealth];
  const optionalServices = [redisHealth];

  // If any critical service is unhealthy, system is unhealthy
  if (criticalServices.some(service => !service.healthy)) {
    return 'unhealthy';
  }

  // If any optional service is unhealthy, system is degraded
  if (optionalServices.some(service => !service.healthy)) {
    return 'degraded';
  }

  // Check other services
  const services = Object.values(currentHealth) as any[];
  const unhealthyServices = services.filter(service => service.status === 'unhealthy').length;
  const degradedServices = services.filter(service => service.status === 'degraded').length;

  if (unhealthyServices > 0) {
    return 'unhealthy';
  }

  if (degradedServices > 0) {
    return 'degraded';
  }

  return 'healthy';
}
