import { NextRequest, NextResponse } from 'next/server';
// import { prisma } from '@/lib/postgresDb';
// import { generateInvitationCode, SECURITY_CONFIG, logRegistrationAttempt } from '@/lib/secureRegistration';

// Mock data storage
const mockInvitations = new Map();

// Simple invitation code generator
function generateInvitationCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  const segments = [];

  for (let i = 0; i < 4; i++) {
    let segment = '';
    for (let j = 0; j < 4; j++) {
      segment += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    segments.push(segment);
  }

  return segments.join('-');
}

// GET /api/schools/invitations - Get invitations for a school
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const schoolId = searchParams.get('schoolId');
    const status = searchParams.get('status'); // 'active', 'used', 'expired'

    if (!schoolId) {
      return NextResponse.json(
        { success: false, message: 'School ID is required' },
        { status: 400 }
      );
    }

    // Build where clause
    const whereClause: any = { schoolId };
    
    if (status === 'active') {
      whereClause.isUsed = false;
      whereClause.expiresAt = { gt: new Date() };
    } else if (status === 'used') {
      whereClause.isUsed = true;
    } else if (status === 'expired') {
      whereClause.isUsed = false;
      whereClause.expiresAt = { lt: new Date() };
    }

    const invitations = await prisma.registrationInvitation.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        code: true,
        studentName: true,
        examLevel: true,
        expiresAt: true,
        isUsed: true,
        usedAt: true,
        usedByStudentId: true,
        createdAt: true
      }
    });

    // Add status to each invitation
    const invitationsWithStatus = invitations.map(invitation => ({
      ...invitation,
      status: invitation.isUsed 
        ? 'used' 
        : invitation.expiresAt < new Date() 
          ? 'expired' 
          : 'active'
    }));

    return NextResponse.json({
      success: true,
      data: invitationsWithStatus,
      message: 'Invitations retrieved successfully'
    });

  } catch (error) {
    console.error('Get invitations error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/schools/invitations - Generate invitation from pre-registration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { preRegistrationId, createdBy } = body;

    if (!preRegistrationId) {
      return NextResponse.json(
        { success: false, message: 'Pre-registration ID is required' },
        { status: 400 }
      );
    }

    // Mock pre-registration data
    const preRegistration = {
      id: preRegistrationId,
      schoolId: 'test-school-1',
      studentName: 'Test Student',
      examLevel: 'O Level',
      verifiedBySchool: true,
      status: 'verified'
    };

    // Check if invitation already exists
    const existingInvitation = Array.from(mockInvitations.values())
      .find((inv: any) =>
        inv.preRegistrationId === preRegistrationId &&
        !inv.isUsed &&
        inv.expiresAt > new Date()
      );

    if (existingInvitation) {
      return NextResponse.json({
        success: true,
        data: {
          id: existingInvitation.id,
          code: existingInvitation.code,
          expiresAt: existingInvitation.expiresAt,
          studentName: existingInvitation.studentName,
          examLevel: existingInvitation.examLevel
        },
        message: 'Active invitation already exists for this pre-registration'
      });
    }

    // Generate invitation code
    const invitationCode = generateInvitationCode();

    // Calculate expiry date (30 days)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    // Create mock invitation
    const invitation = {
      id: `inv_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
      code: invitationCode,
      preRegistrationId,
      schoolId: preRegistration.schoolId,
      studentName: preRegistration.studentName,
      examLevel: preRegistration.examLevel,
      expiresAt,
      isUsed: false,
      usedAt: null,
      createdBy: createdBy || 'Test Admin',
      createdAt: new Date()
    };

    // Store in mock database
    mockInvitations.set(invitation.id, invitation);

    return NextResponse.json({
      success: true,
      data: {
        id: invitation.id,
        code: invitation.code,
        expiresAt: invitation.expiresAt,
        studentName: invitation.studentName,
        examLevel: invitation.examLevel
      },
      message: 'Invitation generated successfully'
    });

  } catch (error) {
    console.error('Generate invitation error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/schools/invitations - Regenerate or extend invitation
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { invitationId, action } = body; // action: 'regenerate' or 'extend'

    if (!invitationId || !action) {
      return NextResponse.json(
        { success: false, message: 'Invitation ID and action are required' },
        { status: 400 }
      );
    }

    const invitation = await prisma.registrationInvitation.findUnique({
      where: { id: invitationId }
    });

    if (!invitation) {
      return NextResponse.json(
        { success: false, message: 'Invitation not found' },
        { status: 404 }
      );
    }

    if (invitation.isUsed) {
      return NextResponse.json(
        { success: false, message: 'Cannot modify used invitation' },
        { status: 400 }
      );
    }

    let updateData: any = {};

    if (action === 'extend') {
      // Extend expiry by another week
      const newExpiry = new Date();
      newExpiry.setDate(newExpiry.getDate() + SECURITY_CONFIG.INVITATION_EXPIRY_DAYS);
      updateData.expiresAt = newExpiry;
    } else if (action === 'regenerate') {
      // Generate new code
      const newCode = generateInvitationCode();
      const newExpiry = new Date();
      newExpiry.setDate(newExpiry.getDate() + SECURITY_CONFIG.INVITATION_EXPIRY_DAYS);
      
      updateData.code = newCode;
      updateData.expiresAt = newExpiry;
    } else {
      return NextResponse.json(
        { success: false, message: 'Invalid action. Use "regenerate" or "extend"' },
        { status: 400 }
      );
    }

    const updatedInvitation = await prisma.registrationInvitation.update({
      where: { id: invitationId },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: {
        id: updatedInvitation.id,
        code: updatedInvitation.code,
        expiresAt: updatedInvitation.expiresAt,
        studentName: updatedInvitation.studentName,
        examLevel: updatedInvitation.examLevel
      },
      message: `Invitation ${action}d successfully`
    });

  } catch (error) {
    console.error('Update invitation error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
