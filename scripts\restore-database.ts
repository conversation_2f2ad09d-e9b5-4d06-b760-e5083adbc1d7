#!/usr/bin/env tsx
/**
 * Database Restore Script
 * Restores database from backups created by backup-database.ts
 */

import { PrismaClient } from '../src/generated/prisma';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const prisma = new PrismaClient();

interface RestoreConfig {
  backupPath: string;
  confirmRestore: boolean;
  dropExisting: boolean;
  restoreData: boolean;
  restoreSchema: boolean;
}

interface RestoreResult {
  timestamp: string;
  success: boolean;
  duration: number;
  filesRestored: string[];
  recordsRestored: number;
  error?: string;
}

async function validateBackupPath(backupPath: string): Promise<{ valid: boolean; files: string[]; metadata?: any }> {
  if (!fs.existsSync(backupPath)) {
    return { valid: false, files: [] };
  }

  const files = fs.readdirSync(backupPath);
  const backupFiles = files.filter(file => 
    file.endsWith('.sql') || 
    file.endsWith('.dump') || 
    file.endsWith('.json')
  );

  let metadata;
  const metadataFile = path.join(backupPath, 'backup-metadata.json');
  if (fs.existsSync(metadataFile)) {
    try {
      metadata = JSON.parse(fs.readFileSync(metadataFile, 'utf8'));
    } catch (error) {
      console.warn('Warning: Could not parse backup metadata');
    }
  }

  return {
    valid: backupFiles.length > 0,
    files: backupFiles,
    metadata
  };
}

async function confirmRestoreOperation(config: RestoreConfig, metadata?: any): Promise<boolean> {
  if (!config.confirmRestore) {
    return true; // Skip confirmation if not required
  }

  console.log('\n⚠️  DATABASE RESTORE CONFIRMATION');
  console.log('================================');
  console.log(`📁 Backup path: ${config.backupPath}`);
  console.log(`🗑️  Drop existing data: ${config.dropExisting ? 'YES' : 'NO'}`);
  console.log(`📋 Restore schema: ${config.restoreSchema ? 'YES' : 'NO'}`);
  console.log(`📊 Restore data: ${config.restoreData ? 'YES' : 'NO'}`);
  
  if (metadata) {
    console.log(`📅 Backup date: ${metadata.timestamp}`);
    console.log(`📊 Total records: ${metadata.totalRecords || 'Unknown'}`);
  }

  console.log('\n⚠️  WARNING: This operation will modify your database!');
  console.log('Type "CONFIRM" to proceed or anything else to cancel:');

  // In a real scenario, you'd use readline for user input
  // For now, we'll assume confirmation based on environment
  const confirmed = process.env.RESTORE_CONFIRMED === 'true';
  
  if (!confirmed) {
    console.log('❌ Restore cancelled by user');
    return false;
  }

  return true;
}

async function restoreFromSQL(sqlFile: string, config: RestoreConfig): Promise<void> {
  const dbUrl = process.env.DATABASE_URL;
  
  if (!dbUrl || !dbUrl.includes('postgresql://')) {
    throw new Error('PostgreSQL DATABASE_URL not found or invalid');
  }

  // Parse connection details
  const url = new URL(dbUrl);
  const host = url.hostname;
  const port = url.port || '5432';
  const database = url.pathname.slice(1);
  const username = url.username;
  const password = url.password;

  // Set environment variable for password
  process.env.PGPASSWORD = password;

  try {
    console.log(`📥 Restoring from SQL file: ${sqlFile}`);

    if (config.dropExisting && sqlFile.includes('full-backup')) {
      console.log('🗑️ Dropping existing database...');
      const dropCmd = `psql -h ${host} -p ${port} -U ${username} -d postgres -c "DROP DATABASE IF EXISTS ${database};"`;
      await execAsync(dropCmd);
      
      console.log('🏗️ Creating new database...');
      const createCmd = `psql -h ${host} -p ${port} -U ${username} -d postgres -c "CREATE DATABASE ${database};"`;
      await execAsync(createCmd);
    }

    console.log('📥 Executing SQL restore...');
    const restoreCmd = `psql -h ${host} -p ${port} -U ${username} -d ${database} -f "${sqlFile}"`;
    await execAsync(restoreCmd);
    
    console.log(`✅ SQL restore completed: ${sqlFile}`);

  } finally {
    delete process.env.PGPASSWORD;
  }
}

async function restoreFromDump(dumpFile: string): Promise<void> {
  const dbUrl = process.env.DATABASE_URL;
  
  if (!dbUrl || !dbUrl.includes('postgresql://')) {
    throw new Error('PostgreSQL DATABASE_URL not found or invalid');
  }

  const url = new URL(dbUrl);
  const host = url.hostname;
  const port = url.port || '5432';
  const database = url.pathname.slice(1);
  const username = url.username;
  const password = url.password;

  process.env.PGPASSWORD = password;

  try {
    console.log(`📥 Restoring from dump file: ${dumpFile}`);
    const restoreCmd = `pg_restore -h ${host} -p ${port} -U ${username} -d ${database} -v "${dumpFile}"`;
    await execAsync(restoreCmd);
    console.log(`✅ Dump restore completed: ${dumpFile}`);
  } finally {
    delete process.env.PGPASSWORD;
  }
}

async function restoreFromJSON(jsonFile: string, config: RestoreConfig): Promise<number> {
  console.log(`📥 Restoring from JSON file: ${jsonFile}`);
  
  const backupData = JSON.parse(fs.readFileSync(jsonFile, 'utf8'));
  let recordsRestored = 0;

  if (!backupData.data) {
    throw new Error('Invalid JSON backup format');
  }

  await prisma.$connect();

  try {
    // Clear existing data if requested
    if (config.dropExisting) {
      console.log('🗑️ Clearing existing data...');
      
      // Clear in reverse dependency order
      await prisma.auditLog.deleteMany();
      await prisma.registrationInvitation.deleteMany();
      await prisma.schoolPreRegistration.deleteMany();
      await prisma.examSession.deleteMany();
      await prisma.examCenter.deleteMany();
      await prisma.school.deleteMany();
      await prisma.subject.deleteMany();
      
      // Clear user tables
      try {
        await prisma.oLevelStudent.deleteMany();
        await prisma.aLevelStudent.deleteMany();
        await prisma.teacherUser.deleteMany();
        await prisma.examinerUser.deleteMany();
      } catch (error) {
        console.log('⚠️ Some user tables may not exist or be accessible');
      }
    }

    // Restore data in dependency order
    const restoreOrder = [
      'subjects',
      'schools', 
      'examCenters',
      'examSessions',
      'schoolPreRegistrations',
      'registrationInvitations',
      'oLevelStudents',
      'aLevelStudents',
      'teachers',
      'examiners',
      'auditLogs'
    ];

    for (const tableName of restoreOrder) {
      const tableData = backupData.data[tableName];
      
      if (!tableData || !Array.isArray(tableData) || tableData.length === 0) {
        console.log(`   ⚠️ No data for ${tableName}`);
        continue;
      }

      console.log(`   📊 Restoring ${tableName}: ${tableData.length} records`);

      try {
        // Convert date strings back to Date objects
        const processedData = tableData.map((record: any) => {
          const processed = { ...record };
          
          // Convert date fields
          ['createdAt', 'updatedAt', 'lastLogin', 'startDate', 'endDate', 
           'registrationStart', 'registrationEnd', 'expiresAt', 'timestamp'].forEach(field => {
            if (processed[field] && typeof processed[field] === 'string') {
              processed[field] = new Date(processed[field]);
            }
          });
          
          return processed;
        });

        // Restore based on table name
        switch (tableName) {
          case 'subjects':
            await prisma.subject.createMany({ data: processedData, skipDuplicates: true });
            break;
          case 'schools':
            await prisma.school.createMany({ data: processedData, skipDuplicates: true });
            break;
          case 'examCenters':
            await prisma.examCenter.createMany({ data: processedData, skipDuplicates: true });
            break;
          case 'examSessions':
            await prisma.examSession.createMany({ data: processedData, skipDuplicates: true });
            break;
          case 'schoolPreRegistrations':
            await prisma.schoolPreRegistration.createMany({ data: processedData, skipDuplicates: true });
            break;
          case 'registrationInvitations':
            await prisma.registrationInvitation.createMany({ data: processedData, skipDuplicates: true });
            break;
          case 'oLevelStudents':
            await prisma.oLevelStudent.createMany({ data: processedData, skipDuplicates: true });
            break;
          case 'aLevelStudents':
            await prisma.aLevelStudent.createMany({ data: processedData, skipDuplicates: true });
            break;
          case 'teachers':
            await prisma.teacherUser.createMany({ data: processedData, skipDuplicates: true });
            break;
          case 'examiners':
            await prisma.examinerUser.createMany({ data: processedData, skipDuplicates: true });
            break;
          case 'auditLogs':
            await prisma.auditLog.createMany({ data: processedData, skipDuplicates: true });
            break;
          default:
            console.log(`   ⚠️ Unknown table: ${tableName}`);
        }

        recordsRestored += tableData.length;
        console.log(`   ✅ ${tableName} restored successfully`);

      } catch (error) {
        console.error(`   ❌ Error restoring ${tableName}:`, error);
      }
    }

  } finally {
    await prisma.$disconnect();
  }

  return recordsRestored;
}

async function performRestore(config: RestoreConfig): Promise<RestoreResult> {
  const startTime = Date.now();
  const result: RestoreResult = {
    timestamp: new Date().toISOString(),
    success: false,
    duration: 0,
    filesRestored: [],
    recordsRestored: 0
  };

  try {
    console.log('🔄 Starting database restore...');

    // Validate backup
    const validation = await validateBackupPath(config.backupPath);
    if (!validation.valid) {
      throw new Error(`Invalid backup path or no backup files found: ${config.backupPath}`);
    }

    console.log(`✅ Found ${validation.files.length} backup files`);

    // Confirm restore operation
    const confirmed = await confirmRestoreOperation(config, validation.metadata);
    if (!confirmed) {
      throw new Error('Restore cancelled by user');
    }

    // Process backup files
    for (const file of validation.files) {
      const filePath = path.join(config.backupPath, file);
      
      if (file.endsWith('.sql') && config.restoreSchema) {
        await restoreFromSQL(filePath, config);
        result.filesRestored.push(file);
      } else if (file.endsWith('.dump')) {
        await restoreFromDump(filePath);
        result.filesRestored.push(file);
      } else if (file.endsWith('.json') && config.restoreData) {
        const records = await restoreFromJSON(filePath, config);
        result.recordsRestored += records;
        result.filesRestored.push(file);
      }
    }

    result.success = true;
    result.duration = Date.now() - startTime;

    console.log('\n✅ Restore completed successfully!');
    console.log(`📄 Files restored: ${result.filesRestored.length}`);
    console.log(`📊 Records restored: ${result.recordsRestored}`);
    console.log(`⏱️ Duration: ${(result.duration / 1000).toFixed(2)}s`);

  } catch (error) {
    result.error = error instanceof Error ? error.message : String(error);
    console.error('❌ Restore failed:', error);
  }

  return result;
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.length === 0) {
    console.log(`
Database Restore Tool

Usage: npm run db:restore <backup-path> [options]

Arguments:
  <backup-path>     Path to backup directory

Options:
  --drop-existing   Drop existing data before restore
  --schema-only     Restore schema only
  --data-only       Restore data only
  --no-confirm      Skip confirmation prompt
  --help            Show this help message

Examples:
  npm run db:restore ./backups/backup-2025-01-08T10-30-00-000Z
  npm run db:restore ./backups/latest -- --drop-existing
  npm run db:restore ./backups/schema-backup -- --schema-only --no-confirm

Environment Variables:
  RESTORE_CONFIRMED=true    Skip confirmation prompt
    `);
    return;
  }

  const config: RestoreConfig = {
    backupPath: args[0],
    confirmRestore: !args.includes('--no-confirm'),
    dropExisting: args.includes('--drop-existing'),
    restoreData: !args.includes('--schema-only'),
    restoreSchema: !args.includes('--data-only')
  };

  await performRestore(config);
}

if (require.main === module) {
  main();
}

export { performRestore };
