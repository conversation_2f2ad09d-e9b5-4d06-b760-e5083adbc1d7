import { NextRequest, NextResponse } from 'next/server';
import { userStorage } from '@/lib/userStorage';
import {
  isRegistrationAllowed,
  validateInvitationCodeFormat,
  logRegistrationAttempt,
  assessRegistrationRisk,
  REGISTRATION_MESSAGES,
  CURRENT_REGISTRATION_MODE,
  RegistrationMode
} from '@/lib/secureRegistration';
import { prisma } from '@/lib/postgresDb';

export async function POST(request: NextRequest) {
  try {
    // Get IP and user agent for security logging
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || '';

    const body = await request.json();
    const {
      fullName,
      email,
      password,
      userType,
      school,
      dateOfBirth,
      candidateNumber,
      // Enhanced student fields
      examLevel,
      gender,
      phoneNumber,
      region,
      schoolCenterNumber,
      parentGuardian<PERSON><PERSON>,
      parentG<PERSON>ianPhone,
      emergencyContactName,
      emergencyContactPhone,
      previousSchool,
      securityQuestion,
      securityAnswer,
      // Picture upload (will be handled separately)
      profilePicture,
      // SECURITY: Invitation code for secure registration
      invitationCode,
      nationalIdNumber
    } = body;

    // SECURITY CHECK: Validate registration is allowed
    const registrationCheck = isRegistrationAllowed();
    if (!registrationCheck.allowed) {
      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'registration',
        false,
        'Registration not allowed: ' + registrationCheck.reason,
        email
      );

      return NextResponse.json(
        { success: false, message: registrationCheck.reason },
        { status: 403 }
      );
    }

    // Validate required fields
    if (!fullName || !email || !password || !userType) {
      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'registration',
        false,
        'Missing required fields',
        email
      );

      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // SECURITY: For students, require invitation code in school-mediated mode
    if (userType === 'student' && CURRENT_REGISTRATION_MODE === RegistrationMode.SCHOOL_MEDIATED) {
      if (!invitationCode) {
        await logRegistrationAttempt(
          ipAddress,
          userAgent,
          'registration',
          false,
          'Missing invitation code',
          email
        );

        return NextResponse.json(
          { success: false, message: REGISTRATION_MESSAGES.INVITATION_REQUIRED },
          { status: 400 }
        );
      }

      // Validate invitation code format
      if (!validateInvitationCodeFormat(invitationCode)) {
        await logRegistrationAttempt(
          ipAddress,
          userAgent,
          'registration',
          false,
          'Invalid invitation code format',
          email
        );

        return NextResponse.json(
          { success: false, message: REGISTRATION_MESSAGES.INVALID_INVITATION },
          { status: 400 }
        );
      }

      // Verify invitation code exists and is valid
      const invitation = await prisma.registrationInvitation.findUnique({
        where: { code: invitationCode },
        include: {
          // We'll add the pre-registration relation later
        }
      });

      if (!invitation) {
        await logRegistrationAttempt(
          ipAddress,
          userAgent,
          'registration',
          false,
          'Invitation code not found',
          email
        );

        return NextResponse.json(
          { success: false, message: REGISTRATION_MESSAGES.INVALID_INVITATION },
          { status: 400 }
        );
      }

      // Check if invitation is expired
      if (invitation.expiresAt < new Date()) {
        await logRegistrationAttempt(
          ipAddress,
          userAgent,
          'registration',
          false,
          'Invitation code expired',
          email
        );

        return NextResponse.json(
          { success: false, message: REGISTRATION_MESSAGES.INVITATION_EXPIRED },
          { status: 400 }
        );
      }

      // Check if invitation is already used
      if (invitation.isUsed) {
        await logRegistrationAttempt(
          ipAddress,
          userAgent,
          'registration',
          false,
          'Invitation code already used',
          email
        );

        return NextResponse.json(
          { success: false, message: REGISTRATION_MESSAGES.INVITATION_USED },
          { status: 400 }
        );
      }

      // Validate that the student details match the invitation
      if (invitation.examLevel !== examLevel) {
        await logRegistrationAttempt(
          ipAddress,
          userAgent,
          'registration',
          false,
          'Exam level mismatch with invitation',
          email
        );

        return NextResponse.json(
          { success: false, message: 'Exam level does not match the invitation' },
          { status: 400 }
        );
      }
    }

    // SECURITY: Risk assessment
    const riskAssessment = await assessRegistrationRisk(
      ipAddress,
      userAgent,
      email,
      nationalIdNumber
    );

    if (riskAssessment.recommendation === 'reject') {
      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'registration',
        false,
        'High risk score: ' + riskAssessment.factors.join(', '),
        email,
        nationalIdNumber
      );

      return NextResponse.json(
        { success: false, message: REGISTRATION_MESSAGES.RISK_TOO_HIGH },
        { status: 403 }
      );
    }

    // Check if user already exists in the specific user type
    if (await userStorage.emailExistsInType(email, userType)) {
      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'registration',
        false,
        'Email already exists in user type',
        email,
        nationalIdNumber
      );

      return NextResponse.json(
        { success: false, message: `A ${userType} account with this email already exists` },
        { status: 409 }
      );
    }

    // Also check if email exists in other user types (optional - for better UX)
    if (await userStorage.emailExists(email)) {
      const existingUser = await userStorage.findByEmail(email);

      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'registration',
        false,
        'Email exists in different user type',
        email,
        nationalIdNumber
      );

      return NextResponse.json(
        { success: false, message: `This email is already registered as a ${existingUser?.userType} account. Please use a different email or login with the correct account type.` },
        { status: 409 }
      );
    }

    // Create new user using PostgreSQL storage
    const newUser = await userStorage.createUser({
      fullName,
      email,
      password, // Will be hashed in userStorage
      userType: userType as 'student' | 'teacher' | 'examiner' | 'admin',
      school: school || undefined,
      dateOfBirth: dateOfBirth || undefined,
      candidateNumber: candidateNumber || undefined,
      registrationStatus: 'confirmed',
      emailVerified: false, // Require email verification
      // Enhanced student fields
      examLevel: examLevel || (userType === 'student' ? 'O Level' : undefined),
      gender: gender || undefined,
      phoneNumber: phoneNumber || undefined,
      region: region || undefined,
      schoolCenterNumber: schoolCenterNumber || undefined,
      parentGuardianName: parentGuardianName || undefined,
      parentGuardianPhone: parentGuardianPhone || undefined,
      emergencyContactName: emergencyContactName || undefined,
      emergencyContactPhone: emergencyContactPhone || undefined,
      previousSchool: previousSchool || undefined,
      securityQuestion: securityQuestion || undefined,
      securityAnswer: securityAnswer || undefined,
      examCenter: userType === 'student' ? 'Default Examination Center' : undefined,
      centerCode: schoolCenterNumber || (userType === 'student' ? 'DEC-001' : undefined),
      subjects: userType === 'student' ? [
        { code: 'ALG', name: 'English Literature', status: 'confirmed' },
        { code: 'AFR', name: 'French', status: 'confirmed' },
        { code: 'AMH', name: 'Mathematics', status: 'confirmed' }
      ] : undefined
    });

    // SECURITY: Mark invitation as used if this was a student registration with invitation
    if (userType === 'student' && invitationCode && CURRENT_REGISTRATION_MODE === RegistrationMode.SCHOOL_MEDIATED) {
      try {
        // Mark invitation as used
        await prisma.registrationInvitation.update({
          where: { code: invitationCode },
          data: {
            isUsed: true,
            usedAt: new Date(),
            usedByStudentId: newUser.id
          }
        });

        // Update pre-registration status
        const invitation = await prisma.registrationInvitation.findUnique({
          where: { code: invitationCode }
        });

        if (invitation) {
          await prisma.schoolPreRegistration.update({
            where: { id: invitation.preRegistrationId },
            data: {
              status: 'completed',
              studentAccountId: newUser.id,
              invitationUsed: true,
              usedAt: new Date()
            }
          });
        }
      } catch (error) {
        console.error('Error updating invitation status:', error);
        // Don't fail registration if invitation update fails
      }
    }

    // If student, register them to their school
    if (userType === 'student' && schoolCenterNumber && examLevel) {
      try {
        // Call the school-student relationship API
        const schoolRegistrationResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/schools/students`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            studentId: newUser.id,
            schoolCenterNumber,
            examLevel,
            studentData: {
              fullName: newUser.fullName,
              email: newUser.email
            }
          })
        });

        if (schoolRegistrationResponse.ok) {
          console.log('Student successfully registered to school:', schoolCenterNumber);
        } else {
          console.warn('Failed to register student to school, but user creation succeeded');
        }
      } catch (error) {
        console.error('Error registering student to school:', error);
        // Don't fail the entire registration if school registration fails
      }
    }

    // Email verification would be sent here in production
    // For now, we'll skip the internal API call to avoid circular dependencies
    console.log('User registered:', newUser.email, 'Verification email would be sent');

    // Log successful registration
    await logRegistrationAttempt(
      ipAddress,
      userAgent,
      'registration',
      true,
      undefined,
      email,
      nationalIdNumber
    );

    // Generate auth token
    const authToken = `auth-token-${newUser.id}-${Date.now()}`;

    // Return success response
    return NextResponse.json({
      success: true,
      data: {
        id: newUser.id,
        email: newUser.email,
        userType: newUser.userType,
        name: newUser.fullName,
        token: authToken,
        permissions: ['read', 'write'],
        lastLogin: new Date().toISOString(),
        emailVerificationSent: true
      },
      message: 'Registration successful. Please check your email to verify your account.'
    });

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Get all registered users (for admin purposes)
export async function GET(request: NextRequest) {
  try {
    const allUsers = userStorage.getAllUsers();

    // Remove password hashes from response for security
    const safeUsers = allUsers.map(user => {
      const { passwordHash, ...safeUser } = user;
      return safeUser;
    });

    return NextResponse.json({
      success: true,
      data: safeUsers,
      message: 'Users retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
