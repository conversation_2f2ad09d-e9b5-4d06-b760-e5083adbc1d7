{"timestamp": "2025-07-08T22:32:24.492Z", "database": {"connection": true, "type": "PostgreSQL", "version": "PostgreSQL 17.4 on x86_64-windows, compiled by msvc-19.43.34808, 64-bit"}, "schemas": {"public": {"exists": true, "tables": ["institution_verifications", "examiner_approvals", "identity_verification", "mobile_verification", "document_verification", "audit_log", "subjects", "exam_centers", "schools", "exam_sessions", "school_pre_registrations", "registration_invitations", "registration_attempts", "professional_registrations"], "recordCounts": {"undefined": -1}}, "o_level_students": {"exists": true, "tables": ["users"], "recordCounts": {"undefined": -1}}, "a_level_students": {"exists": true, "tables": ["users"], "recordCounts": {"undefined": -1}}, "teacher_auth": {"exists": true, "tables": ["users"], "recordCounts": {"undefined": -1}}, "examiner_auth": {"exists": true, "tables": ["users"], "recordCounts": {"undefined": -1}}}, "performance": {"connectionTime": 214, "queryTime": 135}, "issues": [], "recommendations": []}