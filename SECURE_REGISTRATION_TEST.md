# 🧪 Secure Registration System Testing Guide

## 🎯 **Testing the Updated Registration System**

### **What We've Implemented**
- ✅ **Modified existing Register page** instead of creating new pages
- ✅ **Added invitation code validation** for students
- ✅ **Integrated security checks** into the existing form
- ✅ **Added visual security indicators** and notices
- ✅ **Maintained existing UI/UX** while adding security

## 🚀 **Quick Test Instructions**

### **Step 1: Set Up Environment**
```bash
# Copy environment file
cp .env.example .env

# Edit .env with your database credentials
# DATABASE_URL="postgresql://username:password@localhost:5432/gce_system"
```

### **Step 2: Run Database Migration**
```bash
# Push new schema to database
npx prisma db push

# Generate Prisma client
npx prisma generate
```

### **Step 3: Start the Application**
```bash
# Start development server
npm run dev

# Visit the registration page
http://localhost:3000/auth/Register
```

## 🔍 **Testing Scenarios**

### **Scenario 1: Student Registration (Secure Mode)**

1. **Visit Registration Page**: `http://localhost:3000/auth/Register`
2. **Select "Student" Account Type**
3. **Observe Security Features**:
   - 🔐 Security notice at top of page
   - 🛡️ "Secure Registration" badge in header
   - ⚠️ Yellow invitation code section appears
   - 🔒 Submit button disabled until valid invitation

4. **Test Invalid Invitation Code**:
   - Enter: `INVALID-CODE-123`
   - Should show: ❌ "Invalid invitation code" error
   - Submit button remains disabled

5. **Test Valid Invitation Code Format**:
   - Enter: `ABCD-1234-EFGH-56`
   - Should trigger validation API call
   - Will show error if invitation doesn't exist in database

### **Scenario 2: Teacher Registration**

1. **Select "Teacher" Account Type**
2. **Observe**:
   - No invitation code required
   - Form shows school field instead
   - Submit button enabled (if other fields valid)

### **Scenario 3: Registration Disabled Mode**

1. **Edit `src/lib/secureRegistration.ts`**:
   ```typescript
   export const CURRENT_REGISTRATION_MODE = RegistrationMode.OPEN;
   ```

2. **Refresh page**
3. **Observe**:
   - Red warning notice appears
   - "Registration Unavailable" message
   - Submit button disabled

## 🛠️ **Creating Test Data**

### **Create Test Invitation Code**

```sql
-- Connect to your PostgreSQL database and run:

-- 1. Create a test school
INSERT INTO "public"."schools" (id, name, "centerNumber", region, "createdAt", "updatedAt")
VALUES ('test-school-1', 'Test High School', '001', 'Centre', NOW(), NOW());

-- 2. Create a pre-registration
INSERT INTO "public"."school_pre_registrations" (
  id, "schoolId", "studentName", "nationalId", "examLevel", 
  "verifiedBySchool", "verifiedBy", "verificationDate", status, 
  "createdAt", "updatedAt"
)
VALUES (
  'test-prereg-1', 'test-school-1', 'John Doe Test', '123456789', 'O Level',
  true, 'Test Admin', NOW(), 'verified',
  NOW(), NOW()
);

-- 3. Create a test invitation
INSERT INTO "public"."registration_invitations" (
  id, code, "preRegistrationId", "schoolId", "studentName", "examLevel",
  "expiresAt", "isUsed", "createdAt", "updatedAt"
)
VALUES (
  'test-invite-1', 'TEST-1234-ABCD-EF', 'test-prereg-1', 'test-school-1',
  'John Doe Test', 'O Level', 
  NOW() + INTERVAL '7 days', false,
  NOW(), NOW()
);
```

### **Test with Valid Invitation**

1. **Enter invitation code**: `TEST-1234-ABCD-EF`
2. **Should show**: ✅ Valid invitation with student details
3. **Exam level**: Auto-filled to "O Level"
4. **Submit button**: Enabled

## 📋 **Expected Behavior**

### **Security Features Working**
- ✅ **Invitation code required** for students
- ✅ **Real-time validation** of invitation codes
- ✅ **Visual feedback** (green for valid, red for invalid)
- ✅ **Auto-fill exam level** from invitation
- ✅ **Submit button disabled** until valid invitation
- ✅ **Security notices** prominently displayed

### **User Experience**
- ✅ **Existing UI preserved** - no major layout changes
- ✅ **Progressive enhancement** - security added without breaking UX
- ✅ **Clear error messages** for invalid invitations
- ✅ **Visual indicators** for security status

### **API Integration**
- ✅ **Invitation validation API** called on code entry
- ✅ **Registration API** includes invitation code
- ✅ **Error handling** for network issues
- ✅ **Loading states** during validation

## 🐛 **Troubleshooting**

### **Common Issues**

1. **"Environment variable not found: DATABASE_URL"**
   ```bash
   # Create .env file with database URL
   echo 'DATABASE_URL="postgresql://username:password@localhost:5432/gce_system"' > .env
   ```

2. **"Table 'registration_invitations' doesn't exist"**
   ```bash
   # Run database migration
   npx prisma db push
   ```

3. **"Invitation validation fails"**
   - Check if invitation exists in database
   - Verify API endpoint `/api/invitations/validate` is working
   - Check browser network tab for API errors

4. **"Submit button always disabled"**
   - Check if `CURRENT_REGISTRATION_MODE` is set correctly
   - Verify invitation validation is working
   - Check browser console for JavaScript errors

### **Debug Mode**

Add this to your browser console to debug:
```javascript
// Check registration mode
console.log('Registration Mode:', window.localStorage.getItem('registrationMode'));

// Check invitation validation
console.log('Invitation Details:', /* invitation state from React DevTools */);

// Test API directly
fetch('/api/invitations/validate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ code: 'TEST-1234-ABCD-EF' })
}).then(r => r.json()).then(console.log);
```

## ✅ **Success Criteria**

The implementation is successful if:

1. **Students cannot register without valid invitation codes**
2. **Teachers can still register normally**
3. **Invalid invitation codes are rejected with clear error messages**
4. **Valid invitation codes auto-fill student information**
5. **All existing functionality remains intact**
6. **Security notices are prominently displayed**
7. **Submit button behavior reflects security requirements**

## 🎉 **Next Steps**

After successful testing:

1. **Deploy to staging environment**
2. **Train school administrators** on invitation generation
3. **Create user documentation** for the new process
4. **Monitor registration attempts** for security issues
5. **Implement additional security features** (document verification, etc.)

Your GCE registration system now has **enterprise-grade security** while maintaining the existing user experience! 🇨🇲🔐
