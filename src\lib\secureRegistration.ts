// Secure Registration System for GCE
// Implements school-mediated registration with invitation codes

import crypto from 'crypto';
import { prisma } from './postgresDb';

// Registration modes
export enum RegistrationMode {
  OPEN = 'OPEN',                    // Open self-registration (DISABLED for security)
  SCHOOL_MEDIATED = 'SCHOOL_MEDIATED', // School creates invitations
  GOVERNMENT_APPROVED = 'GOVERNMENT_APPROVED', // Government pre-approval
  HYBRID = 'HYBRID'                 // Combination approach
}

// Current registration mode (change this to control registration)
export const CURRENT_REGISTRATION_MODE = RegistrationMode.SCHOOL_MEDIATED;

// Security configuration
export const SECURITY_CONFIG = {
  INVITATION_CODE_LENGTH: 12,
  INVITATION_EXPIRY_DAYS: 7,
  MAX_ATTEMPTS_PER_IP: 5,
  MAX_ATTEMPTS_PER_HOUR: 10,
  RISK_SCORE_THRESHOLD: 70,
  REQUIRED_DOCUMENTS: [
    'nationalId',
    'birthCertificate', 
    'passportPhoto',
    'previousAcademicRecords'
  ]
};

// Invitation code generation
export function generateInvitationCode(): string {
  // Generate a secure 12-digit alphanumeric code
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  
  for (let i = 0; i < SECURITY_CONFIG.INVITATION_CODE_LENGTH; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  // Add checksum for validation
  const checksum = crypto
    .createHash('sha256')
    .update(result)
    .digest('hex')
    .substring(0, 2)
    .toUpperCase();
    
  return `${result.substring(0, 4)}-${result.substring(4, 8)}-${result.substring(8, 12)}-${checksum}`;
}

// Validate invitation code format
export function validateInvitationCodeFormat(code: string): boolean {
  const pattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{2}$/;
  return pattern.test(code);
}

// Risk assessment for registration attempts
export interface RiskAssessment {
  score: number;
  factors: string[];
  recommendation: 'approve' | 'review' | 'reject';
}

export async function assessRegistrationRisk(
  ipAddress: string,
  userAgent: string,
  email: string,
  nationalId?: string,
  additionalData?: any
): Promise<RiskAssessment & { confidence: number; detailedAnalysis: any }> {
  const factors: string[] = [];
  let score = 0;

  try {
    // Check for multiple attempts from same IP
    const recentAttempts = await prisma.registrationAttempt.count({
      where: {
        ipAddress,
        createdAt: {
          gte: new Date(Date.now() - 60 * 60 * 1000) // Last hour
        }
      }
    });

    if (recentAttempts >= SECURITY_CONFIG.MAX_ATTEMPTS_PER_HOUR) {
      factors.push('Multiple attempts from same IP');
      score += 30;
    }

    // Check for duplicate email
    if (email) {
      const existingUser = await prisma.oLevelStudent.findFirst({
        where: { email: email.toLowerCase() }
      });
      
      if (!existingUser) {
        const existingALevel = await prisma.aLevelStudent.findFirst({
          where: { email: email.toLowerCase() }
        });
        
        if (existingALevel) {
          factors.push('Email already registered');
          score += 40;
        }
      } else {
        factors.push('Email already registered');
        score += 40;
      }
    }

    // Check for duplicate national ID
    if (nationalId) {
      const existingByNationalId = await prisma.oLevelStudent.findFirst({
        where: { nationalIdNumber: nationalId }
      });
      
      if (!existingByNationalId) {
        const existingALevelByNationalId = await prisma.aLevelStudent.findFirst({
          where: { nationalIdNumber: nationalId }
        });
        
        if (existingALevelByNationalId) {
          factors.push('National ID already registered');
          score += 50;
        }
      } else {
        factors.push('National ID already registered');
        score += 50;
      }
    }

    // Check user agent patterns
    if (!userAgent || userAgent.length < 10) {
      factors.push('Suspicious user agent');
      score += 15;
    }

    // Determine recommendation
    let recommendation: 'approve' | 'review' | 'reject';
    if (score >= SECURITY_CONFIG.RISK_SCORE_THRESHOLD) {
      recommendation = 'reject';
    } else if (score >= 30) {
      recommendation = 'review';
    } else {
      recommendation = 'approve';
    }

    // Advanced fraud analysis
    const advancedAnalysis = await performAdvancedFraudAnalysis(
      ipAddress, userAgent, email, nationalId, additionalData
    );

    score += advancedAnalysis.riskScore;
    factors.push(...advancedAnalysis.riskFactors);

    // Machine learning-like scoring
    const mlScore = await calculateMLRiskScore(ipAddress, userAgent, email, nationalId);
    score += mlScore.score;
    factors.push(...mlScore.factors);

    // Calculate confidence
    let confidence = Math.max(0, 100 - score);

    return {
      score,
      factors,
      recommendation,
      confidence,
      detailedAnalysis: {
        basicChecks: { score: score - advancedAnalysis.riskScore - mlScore.score },
        advancedAnalysis,
        mlAnalysis: mlScore,
        timestamp: new Date()
      }
    };

  } catch (error) {
    console.error('Risk assessment error:', error);
    return {
      score: 100,
      factors: ['System error during risk assessment'],
      recommendation: 'reject'
    };
  }
}

// Log registration attempt
export async function logRegistrationAttempt(
  ipAddress: string,
  userAgent: string,
  attemptType: string,
  success: boolean,
  failureReason?: string,
  email?: string,
  nationalId?: string
): Promise<void> {
  try {
    const riskAssessment = await assessRegistrationRisk(ipAddress, userAgent, email || '', nationalId);
    
    await prisma.registrationAttempt.create({
      data: {
        ipAddress,
        userAgent,
        email,
        nationalId,
        attemptType,
        success,
        failureReason,
        riskScore: riskAssessment.score,
        riskFactors: riskAssessment.factors
      }
    });
  } catch (error) {
    console.error('Failed to log registration attempt:', error);
  }
}

// Check if registration is allowed based on user type
export function isRegistrationAllowed(userType?: 'student' | 'teacher' | 'examiner'): { allowed: boolean; reason?: string; requiresApproval?: boolean } {
  switch (CURRENT_REGISTRATION_MODE) {
    case RegistrationMode.OPEN:
      return {
        allowed: false,
        reason: 'Open registration is disabled for security reasons. Please contact your institution for proper registration process.'
      };

    case RegistrationMode.SCHOOL_MEDIATED:
      if (userType === 'student') {
        return {
          allowed: true
        };
      } else if (userType === 'teacher') {
        return {
          allowed: true,
          requiresApproval: true,
          reason: 'Teacher registration requires institutional verification and approval.'
        };
      } else if (userType === 'examiner') {
        return {
          allowed: true,
          requiresApproval: true,
          reason: 'Examiner registration requires government approval and security clearance.'
        };
      }
      return { allowed: true };

    case RegistrationMode.GOVERNMENT_APPROVED:
      return {
        allowed: false,
        reason: 'Registration requires government pre-approval. Please contact the Ministry of Education.'
      };

    case RegistrationMode.HYBRID:
      return {
        allowed: true,
        requiresApproval: userType !== 'student'
      };

    default:
      return {
        allowed: false,
        reason: 'Registration is currently disabled.'
      };
  }
}

// Professional registration requirements
export const PROFESSIONAL_REQUIREMENTS = {
  TEACHER: {
    REQUIRED_DOCUMENTS: [
      'teachingLicense',
      'academicCertificates',
      'employmentLetter',
      'nationalId',
      'passportPhoto'
    ],
    REQUIRED_QUALIFICATIONS: [
      'bachelors_degree_minimum',
      'teaching_certification',
      'subject_specialization'
    ],
    MINIMUM_EXPERIENCE: 1, // years
    VERIFICATION_LEVELS: ['institution', 'regional_education_office']
  },
  EXAMINER: {
    REQUIRED_DOCUMENTS: [
      'academicCertificates',
      'professionalCertificates',
      'examiningExperience',
      'securityClearance',
      'nationalId',
      'passportPhoto'
    ],
    REQUIRED_QUALIFICATIONS: [
      'masters_degree_preferred',
      'subject_expertise',
      'examining_certification'
    ],
    MINIMUM_EXPERIENCE: 5, // years
    VERIFICATION_LEVELS: ['ministry_of_education', 'gce_board', 'security_services']
  }
};

// Professional registration status messages
export const PROFESSIONAL_MESSAGES = {
  TEACHER_APPLICATION_SUBMITTED: 'Your teacher registration application has been submitted for institutional verification.',
  TEACHER_UNDER_REVIEW: 'Your application is under review by the education authorities.',
  TEACHER_APPROVED: 'Your teacher registration has been approved. You can now access the system.',
  TEACHER_REJECTED: 'Your teacher registration has been rejected. Please contact support for details.',

  EXAMINER_APPLICATION_SUBMITTED: 'Your examiner registration application has been submitted for government approval.',
  EXAMINER_SECURITY_CHECK: 'Your application is undergoing security clearance verification.',
  EXAMINER_APPROVED: 'Your examiner registration has been approved. You can now access the examination system.',
  EXAMINER_REJECTED: 'Your examiner registration has been rejected. Please contact the Ministry of Education.',

  DOCUMENTS_INCOMPLETE: 'Please upload all required documents before submitting your application.',
  VERIFICATION_PENDING: 'Your documents are being verified by the relevant authorities.',
  BACKGROUND_CHECK_REQUIRED: 'A background check is required for this position.'
};

// Validate required documents
export interface DocumentValidation {
  valid: boolean;
  missingDocuments: string[];
  errors: string[];
}

export function validateRequiredDocuments(documents: Record<string, any>): DocumentValidation {
  const missingDocuments: string[] = [];
  const errors: string[] = [];

  for (const docType of SECURITY_CONFIG.REQUIRED_DOCUMENTS) {
    if (!documents[docType]) {
      missingDocuments.push(docType);
    }
  }

  // Additional validation can be added here
  // e.g., file size, format, content validation

  return {
    valid: missingDocuments.length === 0 && errors.length === 0,
    missingDocuments,
    errors
  };
}

// Generate secure session for registration process
export function generateRegistrationSession(): string {
  return crypto.randomBytes(32).toString('hex');
}

// Constants for UI
export const REGISTRATION_MESSAGES = {
  OPEN_DISABLED: 'For security reasons, students cannot create accounts directly. Please contact your school to receive an invitation code.',
  INVITATION_REQUIRED: 'Registration requires a valid invitation code from your school.',
  INVITATION_EXPIRED: 'Your invitation code has expired. Please contact your school for a new code.',
  INVITATION_USED: 'This invitation code has already been used.',
  INVALID_INVITATION: 'Invalid invitation code. Please check the code and try again.',
  RISK_TOO_HIGH: 'Registration cannot be completed due to security concerns. Please contact support.',
  DOCUMENTS_REQUIRED: 'All required documents must be uploaded before registration can be completed.'
};

// Advanced fraud detection functions
async function performAdvancedFraudAnalysis(
  ipAddress: string,
  userAgent: string,
  email: string,
  nationalId?: string,
  additionalData?: any
): Promise<{ riskScore: number; riskFactors: string[] }> {
  const factors: string[] = [];
  let score = 0;

  try {
    // IP reputation analysis
    const ipAnalysis = await analyzeIPReputation(ipAddress);
    score += ipAnalysis.score;
    factors.push(...ipAnalysis.factors);

    // Email domain analysis
    const emailAnalysis = await analyzeEmailDomain(email);
    score += emailAnalysis.score;
    factors.push(...emailAnalysis.factors);

    // Device fingerprinting
    const deviceAnalysis = await analyzeDeviceFingerprint(userAgent, additionalData);
    score += deviceAnalysis.score;
    factors.push(...deviceAnalysis.factors);

    // Behavioral analysis
    const behaviorAnalysis = await analyzeBehavioralPatterns(additionalData);
    score += behaviorAnalysis.score;
    factors.push(...behaviorAnalysis.factors);

    // Time-based analysis
    const timeAnalysis = await analyzeTimePatterns();
    score += timeAnalysis.score;
    factors.push(...timeAnalysis.factors);

  } catch (error) {
    console.error('Advanced fraud analysis error:', error);
    factors.push('Error in advanced analysis');
    score += 10;
  }

  return { riskScore: score, riskFactors: factors };
}

async function calculateMLRiskScore(
  ipAddress: string,
  userAgent: string,
  email: string,
  nationalId?: string
): Promise<{ score: number; factors: string[] }> {
  const factors: string[] = [];
  let score = 0;

  try {
    // Pattern matching algorithms (mock ML)
    const patterns = await detectSuspiciousPatterns(ipAddress, userAgent, email, nationalId);
    score += patterns.score;
    factors.push(...patterns.factors);

    // Anomaly detection
    const anomalies = await detectAnomalies(email, nationalId);
    score += anomalies.score;
    factors.push(...anomalies.factors);

    // Historical data analysis
    const historical = await analyzeHistoricalData(ipAddress, email);
    score += historical.score;
    factors.push(...historical.factors);

  } catch (error) {
    console.error('ML risk scoring error:', error);
    factors.push('Error in ML analysis');
    score += 5;
  }

  return { score, factors };
}

// IP reputation analysis
async function analyzeIPReputation(ipAddress: string): Promise<{ score: number; factors: string[] }> {
  const factors: string[] = [];
  let score = 0;

  try {
    // Check if IP is from known VPN/proxy services
    if (await isVPNOrProxy(ipAddress)) {
      factors.push('VPN/Proxy detected');
      score += 25;
    }

    // Check IP geolocation consistency
    const location = await getIPLocation(ipAddress);
    if (location && !isCameroonIP(location)) {
      factors.push('Non-Cameroon IP address');
      score += 15;
    }

    // Check for suspicious IP patterns
    if (await isSuspiciousIP(ipAddress)) {
      factors.push('Suspicious IP pattern');
      score += 30;
    }

  } catch (error) {
    console.error('IP analysis error:', error);
  }

  return { score, factors };
}

// Email domain analysis
async function analyzeEmailDomain(email: string): Promise<{ score: number; factors: string[] }> {
  const factors: string[] = [];
  let score = 0;

  try {
    const domain = email.split('@')[1]?.toLowerCase();
    if (!domain) {
      factors.push('Invalid email format');
      return { score: 50, factors };
    }

    // Check for temporary email services
    if (await isTemporaryEmailDomain(domain)) {
      factors.push('Temporary email service');
      score += 40;
    }

    // Check for suspicious domain patterns
    if (await isSuspiciousDomain(domain)) {
      factors.push('Suspicious email domain');
      score += 25;
    }

    // Check domain age and reputation
    const domainInfo = await getDomainInfo(domain);
    if (domainInfo.isNew) {
      factors.push('New domain registration');
      score += 15;
    }

  } catch (error) {
    console.error('Email analysis error:', error);
  }

  return { score, factors };
}

// Device fingerprinting analysis
async function analyzeDeviceFingerprint(userAgent: string, additionalData?: any): Promise<{ score: number; factors: string[] }> {
  const factors: string[] = [];
  let score = 0;

  try {
    // Analyze user agent patterns
    if (await isSuspiciousUserAgent(userAgent)) {
      factors.push('Suspicious user agent');
      score += 20;
    }

    // Check for automation tools
    if (await isAutomatedRequest(userAgent, additionalData)) {
      factors.push('Automated request detected');
      score += 35;
    }

    // Browser consistency checks
    if (additionalData && await hasInconsistentBrowserData(additionalData)) {
      factors.push('Inconsistent browser data');
      score += 25;
    }

  } catch (error) {
    console.error('Device fingerprint analysis error:', error);
  }

  return { score, factors };
}

// Mock implementations for fraud detection functions
async function isVPNOrProxy(ipAddress: string): Promise<boolean> {
  // Mock implementation - in production, integrate with IP intelligence services
  const vpnPatterns = ['10.', '192.168.', '172.16.'];
  return vpnPatterns.some(pattern => ipAddress.startsWith(pattern));
}

async function getIPLocation(ipAddress: string): Promise<any> {
  // Mock implementation - in production, use geolocation services
  return { country: 'CM', region: 'Centre' };
}

function isCameroonIP(location: any): boolean {
  return location.country === 'CM';
}

async function isSuspiciousIP(ipAddress: string): Promise<boolean> {
  // Mock implementation - check against threat intelligence feeds
  return false;
}

async function isTemporaryEmailDomain(domain: string): Promise<boolean> {
  const tempDomains = ['10minutemail.com', 'tempmail.org', 'guerrillamail.com'];
  return tempDomains.includes(domain);
}

async function isSuspiciousDomain(domain: string): Promise<boolean> {
  // Check for suspicious patterns
  return domain.includes('temp') || domain.includes('fake') || domain.length < 4;
}

async function getDomainInfo(domain: string): Promise<{ isNew: boolean }> {
  // Mock implementation - in production, check domain registration date
  return { isNew: false };
}

async function isSuspiciousUserAgent(userAgent: string): Promise<boolean> {
  const suspiciousPatterns = ['bot', 'crawler', 'spider', 'scraper'];
  return suspiciousPatterns.some(pattern => userAgent.toLowerCase().includes(pattern));
}

async function isAutomatedRequest(userAgent: string, additionalData?: any): Promise<boolean> {
  // Check for automation indicators
  return !userAgent || userAgent.length < 10;
}

async function hasInconsistentBrowserData(additionalData: any): Promise<boolean> {
  // Mock implementation - check for browser fingerprint inconsistencies
  return false;
}

async function detectSuspiciousPatterns(ipAddress: string, userAgent: string, email: string, nationalId?: string): Promise<{ score: number; factors: string[] }> {
  const factors: string[] = [];
  let score = 0;

  // Pattern detection logic
  if (email.includes('+')) {
    factors.push('Email alias detected');
    score += 10;
  }

  if (nationalId && nationalId.length !== 9) {
    factors.push('Invalid national ID format');
    score += 20;
  }

  return { score, factors };
}

async function detectAnomalies(email: string, nationalId?: string): Promise<{ score: number; factors: string[] }> {
  const factors: string[] = [];
  let score = 0;

  // Anomaly detection logic
  try {
    // Check for unusual patterns in data
    if (email.split('@')[0].length < 3) {
      factors.push('Unusually short email username');
      score += 15;
    }

  } catch (error) {
    console.error('Anomaly detection error:', error);
  }

  return { score, factors };
}

async function analyzeHistoricalData(ipAddress: string, email: string): Promise<{ score: number; factors: string[] }> {
  const factors: string[] = [];
  let score = 0;

  try {
    // Check historical registration attempts
    const recentAttempts = await prisma.registrationAttempt.count({
      where: {
        ipAddress,
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      }
    });

    if (recentAttempts > 5) {
      factors.push('Multiple recent attempts from same IP');
      score += 30;
    }

  } catch (error) {
    console.error('Historical analysis error:', error);
  }

  return { score, factors };
}

async function analyzeBehavioralPatterns(additionalData?: any): Promise<{ score: number; factors: string[] }> {
  const factors: string[] = [];
  let score = 0;

  // Behavioral analysis logic
  if (additionalData?.formFillTime && additionalData.formFillTime < 30) {
    factors.push('Unusually fast form completion');
    score += 20;
  }

  return { score, factors };
}

async function analyzeTimePatterns(): Promise<{ score: number; factors: string[] }> {
  const factors: string[] = [];
  let score = 0;

  const hour = new Date().getHours();

  // Check for unusual registration times
  if (hour < 6 || hour > 22) {
    factors.push('Registration at unusual hours');
    score += 10;
  }

  return { score, factors };
}
