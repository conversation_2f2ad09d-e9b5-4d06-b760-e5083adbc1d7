#!/bin/bash

# Production Deployment Script for GCE System
# This script handles the complete deployment process

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="$PROJECT_ROOT/backups"
LOG_FILE="$PROJECT_ROOT/logs/deployment-$(date +%Y%m%d-%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker is not running. Please start Docker service."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if .env.production file exists
    if [[ ! -f "$PROJECT_ROOT/.env.production" ]]; then
        error ".env.production file not found. Please create it with production environment variables."
    fi
    
    # Check if SSL certificates exist
    if [[ ! -f "$PROJECT_ROOT/config/ssl/gce.cm.crt" ]] || [[ ! -f "$PROJECT_ROOT/config/ssl/gce.cm.key" ]]; then
        warning "SSL certificates not found. HTTPS will not work properly."
    fi
    
    success "Prerequisites check completed"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$PROJECT_ROOT/backups"
    mkdir -p "$PROJECT_ROOT/uploads"
    mkdir -p "$PROJECT_ROOT/certificates"
    mkdir -p "$PROJECT_ROOT/config/ssl"
    
    success "Directories created"
}

# Backup current deployment
backup_current() {
    log "Creating backup of current deployment..."
    
    local backup_name="backup-$(date +%Y%m%d-%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    mkdir -p "$backup_path"
    
    # Backup database
    if docker-compose -f docker-compose.prod.yml ps postgres | grep -q "Up"; then
        log "Backing up database..."
        docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump -U "$POSTGRES_USER" "$POSTGRES_DB" > "$backup_path/database.sql"
    fi
    
    # Backup volumes
    log "Backing up application data..."
    if [[ -d "$PROJECT_ROOT/uploads" ]]; then
        cp -r "$PROJECT_ROOT/uploads" "$backup_path/"
    fi
    
    if [[ -d "$PROJECT_ROOT/certificates" ]]; then
        cp -r "$PROJECT_ROOT/certificates" "$backup_path/"
    fi
    
    # Backup configuration
    if [[ -f "$PROJECT_ROOT/.env.production" ]]; then
        cp "$PROJECT_ROOT/.env.production" "$backup_path/"
    fi
    
    echo "$backup_name" > "$PROJECT_ROOT/.last_backup"
    success "Backup created: $backup_name"
}

# Build Docker images
build_images() {
    log "Building Docker images..."
    
    cd "$PROJECT_ROOT"
    
    # Build the main application image
    docker build -t gce-system:latest .
    
    # Tag with timestamp for rollback capability
    local timestamp=$(date +%Y%m%d-%H%M%S)
    docker tag gce-system:latest "gce-system:$timestamp"
    
    success "Docker images built successfully"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    cd "$PROJECT_ROOT"
    
    # Start only the database for migrations
    docker-compose -f docker-compose.prod.yml up -d postgres redis
    
    # Wait for database to be ready
    log "Waiting for database to be ready..."
    sleep 30
    
    # Run migrations
    docker-compose -f docker-compose.prod.yml run --rm app1 npx prisma migrate deploy
    
    success "Database migrations completed"
}

# Deploy application
deploy_application() {
    log "Deploying application..."
    
    cd "$PROJECT_ROOT"
    
    # Load environment variables
    export $(cat .env.production | xargs)
    
    # Stop existing containers gracefully
    log "Stopping existing containers..."
    docker-compose -f docker-compose.prod.yml down --timeout 30
    
    # Start new deployment
    log "Starting new deployment..."
    docker-compose -f docker-compose.prod.yml up -d
    
    success "Application deployed"
}

# Health check
health_check() {
    log "Performing health checks..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log "Health check attempt $attempt/$max_attempts"
        
        # Check if containers are running
        if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
            # Check application health endpoint
            if curl -f -s http://localhost/health > /dev/null; then
                success "Health check passed"
                return 0
            fi
        fi
        
        sleep 10
        ((attempt++))
    done
    
    error "Health check failed after $max_attempts attempts"
}

# Cleanup old images and containers
cleanup() {
    log "Cleaning up old Docker images and containers..."
    
    # Remove old images (keep last 3 versions)
    docker images gce-system --format "table {{.Tag}}" | grep -E '^[0-9]{8}-[0-9]{6}$' | sort -r | tail -n +4 | xargs -r docker rmi "gce-system:" 2>/dev/null || true
    
    # Remove unused containers and networks
    docker container prune -f
    docker network prune -f
    
    # Remove old backups (keep last 10)
    find "$BACKUP_DIR" -maxdepth 1 -type d -name "backup-*" | sort -r | tail -n +11 | xargs -r rm -rf
    
    success "Cleanup completed"
}

# Rollback function
rollback() {
    log "Rolling back to previous deployment..."
    
    if [[ ! -f "$PROJECT_ROOT/.last_backup" ]]; then
        error "No backup found for rollback"
    fi
    
    local backup_name=$(cat "$PROJECT_ROOT/.last_backup")
    local backup_path="$BACKUP_DIR/$backup_name"
    
    if [[ ! -d "$backup_path" ]]; then
        error "Backup directory not found: $backup_path"
    fi
    
    # Stop current deployment
    docker-compose -f docker-compose.prod.yml down
    
    # Restore database
    if [[ -f "$backup_path/database.sql" ]]; then
        log "Restoring database..."
        docker-compose -f docker-compose.prod.yml up -d postgres
        sleep 30
        docker-compose -f docker-compose.prod.yml exec -T postgres psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" < "$backup_path/database.sql"
    fi
    
    # Restore files
    if [[ -d "$backup_path/uploads" ]]; then
        rm -rf "$PROJECT_ROOT/uploads"
        cp -r "$backup_path/uploads" "$PROJECT_ROOT/"
    fi
    
    if [[ -d "$backup_path/certificates" ]]; then
        rm -rf "$PROJECT_ROOT/certificates"
        cp -r "$backup_path/certificates" "$PROJECT_ROOT/"
    fi
    
    # Start previous version
    docker-compose -f docker-compose.prod.yml up -d
    
    success "Rollback completed"
}

# Main deployment function
main() {
    log "Starting production deployment of GCE System"
    
    # Parse command line arguments
    case "${1:-deploy}" in
        "deploy")
            check_root
            check_prerequisites
            create_directories
            backup_current
            build_images
            run_migrations
            deploy_application
            health_check
            cleanup
            success "Deployment completed successfully!"
            ;;
        "rollback")
            rollback
            ;;
        "health")
            health_check
            ;;
        "backup")
            backup_current
            ;;
        "cleanup")
            cleanup
            ;;
        *)
            echo "Usage: $0 {deploy|rollback|health|backup|cleanup}"
            echo ""
            echo "Commands:"
            echo "  deploy   - Full production deployment (default)"
            echo "  rollback - Rollback to previous deployment"
            echo "  health   - Run health checks"
            echo "  backup   - Create backup only"
            echo "  cleanup  - Cleanup old images and backups"
            exit 1
            ;;
    esac
}

# Trap errors and cleanup
trap 'error "Deployment failed. Check logs at $LOG_FILE"' ERR

# Run main function
main "$@"
