# PostgreSQL Setup Script for GCE System
# This script sets up PostgreSQL for the automated results system

Write-Host "🐘 Setting up PostgreSQL for GCE System..." -ForegroundColor Green

# Check if PostgreSQL is installed
$psqlPath = Get-Command psql -ErrorAction SilentlyContinue
if (-not $psqlPath) {
    Write-Host "❌ PostgreSQL not found. Installing PostgreSQL..." -ForegroundColor Yellow
    
    # Try to install PostgreSQL using winget
    try {
        winget install PostgreSQL.PostgreSQL
        Write-Host "✅ PostgreSQL installed successfully!" -ForegroundColor Green
        Write-Host "⚠️  Please restart your terminal and run this script again." -ForegroundColor Yellow
        exit 0
    }
    catch {
        Write-Host "❌ Failed to install PostgreSQL automatically." -ForegroundColor Red
        Write-Host "Please install PostgreSQL manually from: https://www.postgresql.org/download/windows/" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "✅ PostgreSQL found!" -ForegroundColor Green

# Database configuration
$DB_NAME = "gce_system"
$DB_USER = "gce_user"
$DB_PASSWORD = "gce_password"
$DB_HOST = "localhost"
$DB_PORT = "5432"

Write-Host "🔧 Setting up database and user..." -ForegroundColor Blue

# Create database and user
$createDbScript = @'
DROP DATABASE IF EXISTS gce_system;
CREATE DATABASE gce_system;

DROP USER IF EXISTS gce_user;
CREATE USER gce_user WITH PASSWORD 'gce_password';

GRANT ALL PRIVILEGES ON DATABASE gce_system TO gce_user;
ALTER USER gce_user CREATEDB;

\c gce_system

GRANT ALL ON SCHEMA public TO gce_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO gce_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO gce_user;

CREATE SCHEMA IF NOT EXISTS o_level_students;
CREATE SCHEMA IF NOT EXISTS a_level_students;
CREATE SCHEMA IF NOT EXISTS teacher_auth;
CREATE SCHEMA IF NOT EXISTS examiner_auth;

GRANT ALL ON SCHEMA o_level_students TO gce_user;
GRANT ALL ON SCHEMA a_level_students TO gce_user;
GRANT ALL ON SCHEMA teacher_auth TO gce_user;
GRANT ALL ON SCHEMA examiner_auth TO gce_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO gce_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA o_level_students GRANT ALL ON TABLES TO gce_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA a_level_students GRANT ALL ON TABLES TO gce_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA teacher_auth GRANT ALL ON TABLES TO gce_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA examiner_auth GRANT ALL ON TABLES TO gce_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO gce_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA o_level_students GRANT ALL ON SEQUENCES TO gce_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA a_level_students GRANT ALL ON SEQUENCES TO gce_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA teacher_auth GRANT ALL ON SEQUENCES TO gce_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA examiner_auth GRANT ALL ON SEQUENCES TO gce_user;
'@

# Save the SQL script to a temporary file
$tempSqlFile = "temp_setup.sql"
$createDbScript | Out-File -FilePath $tempSqlFile -Encoding UTF8

try {
    # Execute the SQL script
    Write-Host "🔧 Creating database and user..." -ForegroundColor Blue
    psql -U postgres -f $tempSqlFile
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database and user created successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to create database and user." -ForegroundColor Red
        Write-Host "Please ensure PostgreSQL is running and you have admin privileges." -ForegroundColor Yellow
        exit 1
    }
}
catch {
    Write-Host "❌ Error executing SQL script: $_" -ForegroundColor Red
    exit 1
}
finally {
    # Clean up temporary file
    if (Test-Path $tempSqlFile) {
        Remove-Item $tempSqlFile
    }
}

Write-Host "🔧 Installing Node.js dependencies..." -ForegroundColor Blue
npm install

Write-Host "🔧 Generating Prisma client..." -ForegroundColor Blue
npx prisma generate

Write-Host "🔧 Running database migrations..." -ForegroundColor Blue
npx prisma migrate dev --name "initial_postgresql_setup"

Write-Host "🌱 Seeding database with initial data..." -ForegroundColor Blue
npx prisma db seed

Write-Host "✅ PostgreSQL setup completed successfully!" -ForegroundColor Green
Write-Host "📊 Database: $DB_NAME" -ForegroundColor Cyan
Write-Host "👤 User: $DB_USER" -ForegroundColor Cyan
Write-Host "🔗 Connection: postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}" -ForegroundColor Cyan

Write-Host "`n🚀 Next steps:" -ForegroundColor Yellow
Write-Host "1. Update your .env file with the correct DATABASE_URL" -ForegroundColor White
Write-Host "2. Run 'npm run dev' to start the development server" -ForegroundColor White
Write-Host "3. Access the application at http://localhost:3000" -ForegroundColor White

Write-Host "`n📝 Database Management Commands:" -ForegroundColor Yellow
Write-Host "- View database: npx prisma studio" -ForegroundColor White
Write-Host "- Reset database: npx prisma migrate reset" -ForegroundColor White
Write-Host "- Create migration: npx prisma migrate dev --name <migration_name>" -ForegroundColor White
