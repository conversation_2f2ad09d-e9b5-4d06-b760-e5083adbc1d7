// Test student login functionality
const http = require('http');

function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonBody
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testStudentLogin() {
  console.log('🎓 Testing Student Login Functionality...\n');

  // Test 1: Demo Student Login
  console.log('1. Testing Demo Student Login...');
  console.log('   Email: <EMAIL>');
  console.log('   Password: demo123');
  
  try {
    const loginData = {
      email: '<EMAIL>',
      password: 'demo123',
      userType: 'student'
    };

    const result = await makeRequest('POST', '/api/auth/login', loginData);
    console.log(`   Status: ${result.status}`);
    
    if (result.status === 200 && result.data.success) {
      console.log('   ✅ Login successful!');
      console.log(`   Student ID: ${result.data.user?.id || 'N/A'}`);
      console.log(`   Full Name: ${result.data.user?.fullName || 'N/A'}`);
      console.log(`   Token: ${result.data.token ? 'Generated ✅' : 'Not provided ❌'}`);
      
      // Test accessing student dashboard
      if (result.data.token) {
        console.log('\n   Testing dashboard access...');
        const dashboardResult = await makeRequest('GET', '/api/students/demo-student', null, {
          'Authorization': `Bearer ${result.data.token}`
        });
        console.log(`   Dashboard Status: ${dashboardResult.status}`);
        if (dashboardResult.status === 200) {
          console.log('   ✅ Dashboard accessible!');
        }
      }
    } else {
      console.log('   ❌ Login failed');
      console.log(`   Response:`, result.data);
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
  }

  // Test 2: Second Student Account
  console.log('\n2. Testing Second Student Account...');
  console.log('   Email: <EMAIL>');
  console.log('   Password: student123');
  
  try {
    const loginData = {
      email: '<EMAIL>',
      password: 'student123',
      userType: 'student'
    };

    const result = await makeRequest('POST', '/api/auth/login', loginData);
    console.log(`   Status: ${result.status}`);
    
    if (result.status === 200 && result.data.success) {
      console.log('   ✅ Login successful!');
      console.log(`   Student ID: ${result.data.user?.id || 'N/A'}`);
      console.log(`   Full Name: ${result.data.user?.fullName || 'N/A'}`);
    } else {
      console.log('   ❌ Login failed');
      console.log(`   Response:`, result.data);
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
  }

  // Test 3: Check if we can access student data
  console.log('\n3. Testing Student Data Access...');
  try {
    const studentResult = await makeRequest('GET', '/api/students/demo-student');
    console.log(`   Student Data Status: ${studentResult.status}`);
    
    if (studentResult.status === 200) {
      console.log('   ✅ Student data accessible!');
      console.log(`   Student: ${studentResult.data.data?.fullName || 'N/A'}`);
    } else if (studentResult.status === 401) {
      console.log('   🔒 Authentication required (expected for security)');
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
  }

  console.log('\n🎓 Student login testing complete!');
  console.log('\n📋 For manual testing in browser:');
  console.log('   1. Go to: http://localhost:3000/auth/Login');
  console.log('   2. Use credentials: <EMAIL> / demo123');
  console.log('   3. Select "Student" as user type');
  console.log('   4. Note: CSRF protection may require browser-based login');
}

testStudentLogin().catch(console.error);
