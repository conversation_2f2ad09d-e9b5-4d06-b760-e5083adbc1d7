'use client';

import { useState, useEffect } from 'react';
import SchoolsLayout from '@/components/layouts/SchoolsLayout';
import { 
  Users, Plus, Search, Filter, Download, 
  CheckCircle, Clock, AlertTriangle, Key,
  Eye, Edit, Trash2, Send
} from 'lucide-react';

interface Student {
  id: string;
  fullName: string;
  nationalId: string;
  examLevel: 'O Level' | 'A Level';
  status: 'pending' | 'verified' | 'invitation_sent' | 'registered';
  dateOfBirth: string;
  gender: 'Male' | 'Female';
  parentGuardianName: string;
  parentGuardianPhone: string;
  invitationCode?: string;
  invitationExpiry?: string;
  createdAt: string;
  verifiedAt?: string;
  registeredAt?: string;
}

export default function SchoolStudents() {
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [examLevelFilter, setExamLevelFilter] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [showInvitationModal, setShowInvitationModal] = useState(false);

  // Load students on component mount
  useEffect(() => {
    loadStudents();
  }, []);

  const loadStudents = async () => {
    try {
      setLoading(true);
      // Get school ID from localStorage or context
      const schoolId = localStorage.getItem('schoolId') || 'default-school';
      
      const response = await fetch(`/api/schools/pre-registration?schoolId=${schoolId}`);
      const result = await response.json();
      
      if (result.success) {
        setStudents(result.data || []);
      }
    } catch (error) {
      console.error('Error loading students:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddStudent = async (studentData: any) => {
    try {
      const schoolId = localStorage.getItem('schoolId') || 'default-school';
      const response = await fetch('/api/schools/pre-registration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...studentData,
          schoolId,
          verifiedBy: localStorage.getItem('userName') || 'School Admin'
        })
      });

      const result = await response.json();
      if (result.success) {
        await loadStudents();
        setShowAddModal(false);
        alert('Student added successfully!');
      } else {
        alert('Error adding student: ' + result.message);
      }
    } catch (error) {
      console.error('Error adding student:', error);
      alert('Error adding student. Please try again.');
    }
  };

  const handleGenerateInvitation = async (student: Student) => {
    try {
      const response = await fetch('/api/schools/invitations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          preRegistrationId: student.id,
          createdBy: localStorage.getItem('userName') || 'School Admin'
        })
      });

      const result = await response.json();
      if (result.success) {
        await loadStudents();
        setSelectedStudent({
          ...student,
          invitationCode: result.data.code,
          invitationExpiry: result.data.expiresAt,
          status: 'invitation_sent'
        });
        setShowInvitationModal(true);
      } else {
        alert('Error generating invitation: ' + result.message);
      }
    } catch (error) {
      console.error('Error generating invitation:', error);
      alert('Error generating invitation. Please try again.');
    }
  };

  const filteredStudents = students.filter(student => {
    const matchesSearch = student.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.nationalId.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || student.status === statusFilter;
    const matchesExamLevel = examLevelFilter === 'all' || student.examLevel === examLevelFilter;
    
    return matchesSearch && matchesStatus && matchesExamLevel;
  });

  const getStatusBadge = (status: string) => {
    const styles = {
      pending: 'bg-yellow-100 text-yellow-800',
      verified: 'bg-blue-100 text-blue-800',
      invitation_sent: 'bg-green-100 text-green-800',
      registered: 'bg-purple-100 text-purple-800'
    };
    
    const icons = {
      pending: <Clock className="w-4 h-4 mr-1" />,
      verified: <CheckCircle className="w-4 h-4 mr-1" />,
      invitation_sent: <Send className="w-4 h-4 mr-1" />,
      registered: <Users className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[status as keyof typeof styles]}`}>
        {icons[status as keyof typeof icons]}
        {status.replace('_', ' ').toUpperCase()}
      </span>
    );
  };

  return (
    <SchoolsLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Student Management</h1>
            <p className="text-gray-600">Manage student pre-registration and invitation codes</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Student
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">{students.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending Verification</p>
                <p className="text-2xl font-bold text-gray-900">
                  {students.filter(s => s.status === 'pending').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Send className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Invitations Sent</p>
                <p className="text-2xl font-bold text-gray-900">
                  {students.filter(s => s.status === 'invitation_sent').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Registered</p>
                <p className="text-2xl font-bold text-gray-900">
                  {students.filter(s => s.status === 'registered').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="verified">Verified</option>
              <option value="invitation_sent">Invitation Sent</option>
              <option value="registered">Registered</option>
            </select>
            <select
              value={examLevelFilter}
              onChange={(e) => setExamLevelFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Levels</option>
              <option value="O Level">O Level</option>
              <option value="A Level">A Level</option>
            </select>
            <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center">
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Students Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Student
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Exam Level
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Invitation Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                      Loading students...
                    </td>
                  </tr>
                ) : filteredStudents.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                      No students found
                    </td>
                  </tr>
                ) : (
                  filteredStudents.map((student) => (
                    <tr key={student.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{student.fullName}</div>
                          <div className="text-sm text-gray-500">ID: {student.nationalId}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900">{student.examLevel}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(student.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {student.invitationCode ? (
                          <div>
                            <div className="text-sm font-mono text-gray-900">{student.invitationCode}</div>
                            <div className="text-xs text-gray-500">
                              Expires: {student.invitationExpiry && new Date(student.invitationExpiry).toLocaleDateString()}
                            </div>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">Not generated</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          {student.status === 'verified' && !student.invitationCode && (
                            <button
                              onClick={() => handleGenerateInvitation(student)}
                              className="text-blue-600 hover:text-blue-900 flex items-center"
                            >
                              <Key className="w-4 h-4 mr-1" />
                              Generate Code
                            </button>
                          )}
                          {student.invitationCode && (
                            <button
                              onClick={() => {
                                setSelectedStudent(student);
                                setShowInvitationModal(true);
                              }}
                              className="text-green-600 hover:text-green-900 flex items-center"
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              View Code
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Add Student Modal */}
      {showAddModal && (
        <AddStudentModal
          onClose={() => setShowAddModal(false)}
          onSubmit={handleAddStudent}
        />
      )}

      {/* Invitation Code Modal */}
      {showInvitationModal && selectedStudent && (
        <InvitationCodeModal
          student={selectedStudent}
          onClose={() => setShowInvitationModal(false)}
        />
      )}
    </SchoolsLayout>
  );
}

// Add Student Modal Component
function AddStudentModal({ onClose, onSubmit }: { onClose: () => void; onSubmit: (data: any) => void }) {
  const [formData, setFormData] = useState({
    studentName: '',
    nationalId: '',
    examLevel: 'O Level',
    dateOfBirth: '',
    gender: 'Male',
    parentGuardianName: '',
    parentGuardianPhone: '',
    academicYear: '2024/2025'
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Add New Student</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Student Name *</label>
            <input
              type="text"
              required
              value={formData.studentName}
              onChange={(e) => setFormData({...formData, studentName: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">National ID *</label>
            <input
              type="text"
              required
              value={formData.nationalId}
              onChange={(e) => setFormData({...formData, nationalId: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Exam Level *</label>
              <select
                value={formData.examLevel}
                onChange={(e) => setFormData({...formData, examLevel: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="O Level">O Level</option>
                <option value="A Level">A Level</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Gender *</label>
              <select
                value={formData.gender}
                onChange={(e) => setFormData({...formData, gender: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="Male">Male</option>
                <option value="Female">Female</option>
              </select>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Date of Birth *</label>
            <input
              type="date"
              required
              value={formData.dateOfBirth}
              onChange={(e) => setFormData({...formData, dateOfBirth: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Parent/Guardian Name *</label>
            <input
              type="text"
              required
              value={formData.parentGuardianName}
              onChange={(e) => setFormData({...formData, parentGuardianName: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Parent/Guardian Phone *</label>
            <input
              type="tel"
              required
              value={formData.parentGuardianPhone}
              onChange={(e) => setFormData({...formData, parentGuardianPhone: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Add Student
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Invitation Code Modal Component
function InvitationCodeModal({ student, onClose }: { student: Student; onClose: () => void }) {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Invitation code copied to clipboard!');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Invitation Code Details</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Student Name</label>
            <p className="text-gray-900">{student.fullName}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Exam Level</label>
            <p className="text-gray-900">{student.examLevel}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Invitation Code</label>
            <div className="flex items-center space-x-2">
              <code className="bg-gray-100 px-3 py-2 rounded font-mono text-lg flex-1">
                {student.invitationCode}
              </code>
              <button
                onClick={() => copyToClipboard(student.invitationCode!)}
                className="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Copy
              </button>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Expires</label>
            <p className="text-gray-900">
              {student.invitationExpiry && new Date(student.invitationExpiry).toLocaleDateString()}
            </p>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Instructions for Student:</h4>
            <ol className="text-sm text-blue-800 space-y-1">
              <li>1. Visit the GCE registration website</li>
              <li>2. Select "Student" account type</li>
              <li>3. Enter this invitation code when prompted</li>
              <li>4. Complete the registration form</li>
              <li>5. Submit and wait for confirmation</li>
            </ol>
          </div>
        </div>
        <div className="flex justify-end pt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
