// Test TANHANANG SETOU PRINCELY login
const http = require('http');

function testLogin() {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      email: '<EMAIL>',
      password: 'demo123',
      userType: 'student'
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.write(data);
    req.end();
  });
}

function testStudentAPI(studentId, token) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/students/${studentId}?examLevel=${encodeURIComponent('A Level')}`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.end();
  });
}

async function main() {
  console.log('🎓 Testing TANHANANG SETOU PRINCELY Login...\n');
  
  try {
    // Test login
    console.log('1. Testing login...');
    const loginResult = await testLogin();
    console.log(`   Status: ${loginResult.status}`);
    
    if (loginResult.status === 200 && loginResult.data.success) {
      console.log('   ✅ LOGIN SUCCESSFUL!');
      console.log(`   Name: ${loginResult.data.data.name}`);
      console.log(`   Email: ${loginResult.data.data.email}`);
      console.log(`   User Type: ${loginResult.data.data.userType}`);
      console.log(`   Exam Level: ${loginResult.data.data.examLevel}`);
      console.log(`   Token: ${loginResult.data.data.token ? 'Generated ✅' : 'Missing ❌'}`);
      
      // Test student API
      console.log('\n2. Testing student API...');
      const studentResult = await testStudentAPI(loginResult.data.data.id, loginResult.data.data.token);
      console.log(`   Status: ${studentResult.status}`);
      
      if (studentResult.status === 200) {
        console.log('   ✅ STUDENT DATA LOADED!');
        const student = studentResult.data.data; // Extract nested data
        console.log(`   Full Name: ${student.fullName}`);
        console.log(`   Candidate Number: ${student.candidateNumber}`);
        console.log(`   Exam Center: ${student.examCenter}`);
        console.log(`   Center Code: ${student.centerCode}`);
        console.log(`   Phone: ${student.phoneNumber}`);
        console.log(`   Region: ${student.region}`);
        console.log(`   Subjects: ${student.subjects?.length || 0} registered`);
        
        console.log('\n🎉 PERSONALIZED DEMO IS READY!');
        console.log('\n📋 Demo Instructions:');
        console.log('   1. Open browser: http://localhost:3000/auth/Login');
        console.log('   2. Email: <EMAIL> (pre-filled)');
        console.log('   3. Password: demo123');
        console.log('   4. User Type: Student');
        console.log('   5. Click Login');
        console.log('\n✨ Your personal student portal will display:');
        console.log(`   - Name: ${student.fullName}`);
        console.log(`   - Candidate: ${student.candidateNumber}`);
        console.log(`   - Center: ${student.examCenter}`);
        console.log(`   - Subjects: ${student.subjects?.length || 0} A Level subjects`);
        
      } else {
        console.log('   ❌ Student API failed');
        console.log(`   Response:`, studentResult.data);
      }
      
    } else {
      console.log('   ❌ Login failed');
      console.log(`   Response:`, loginResult.data);
    }
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

main();
