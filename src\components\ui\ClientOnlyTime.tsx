'use client';

import { useState, useEffect } from 'react';

interface ClientOnlyTimeProps {
  format?: 'time' | 'date' | 'datetime';
  className?: string;
  updateInterval?: number; // in milliseconds, default 60000 (1 minute)
  placeholder?: string;
}

/**
 * A client-side only time display component that prevents hydration mismatches
 * by only rendering the actual time after the component has mounted on the client.
 */
export default function ClientOnlyTime({ 
  format = 'time', 
  className = '',
  updateInterval = 60000,
  placeholder = ''
}: ClientOnlyTimeProps) {
  const [mounted, setMounted] = useState(false);
  const [currentTime, setCurrentTime] = useState<string>('');

  useEffect(() => {
    setMounted(true);
    
    const updateTime = () => {
      const now = new Date();
      let formattedTime = '';

      switch (format) {
        case 'time':
          formattedTime = now.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          });
          break;
        case 'date':
          formattedTime = now.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
          break;
        case 'datetime':
          formattedTime = now.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
          break;
        default:
          formattedTime = now.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          });
      }

      setCurrentTime(formattedTime);
    };

    // Set initial time
    updateTime();
    
    // Update time at specified interval
    const timer = setInterval(updateTime, updateInterval);
    
    return () => clearInterval(timer);
  }, [format, updateInterval]);

  // Render placeholder during SSR and before mounting
  if (!mounted) {
    return <span className={className}>{placeholder}</span>;
  }

  return <span className={className}>{currentTime}</span>;
}
