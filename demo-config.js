// Demo Configuration Helper
// Temporarily modify rate limits for presentation purposes

const fs = require('fs');
const path = require('path');

const securityFilePath = path.join(__dirname, 'src', 'lib', 'security.ts');

console.log('🎭 Demo Configuration Helper');
console.log('');

// Read the current security configuration
if (fs.existsSync(securityFilePath)) {
  const content = fs.readFileSync(securityFilePath, 'utf8');
  
  console.log('📊 Current Rate Limit Settings:');
  
  // Extract current settings
  const windowMatch = content.match(/WINDOW_MS:\s*(\d+)/);
  const maxLoginMatch = content.match(/MAX_LOGIN_ATTEMPTS:\s*(\d+)/);
  const lockoutMatch = content.match(/LOCKOUT_DURATION:\s*(\d+)/);
  
  if (windowMatch) {
    const windowMs = parseInt(windowMatch[1]);
    console.log(`   Window: ${windowMs / (60 * 1000)} minutes`);
  }
  
  if (maxLoginMatch) {
    console.log(`   Max login attempts: ${maxLoginMatch[1]}`);
  }
  
  if (lockoutMatch) {
    const lockoutMs = parseInt(lockoutMatch[1]);
    console.log(`   Lockout duration: ${lockoutMs / (60 * 1000)} minutes`);
  }
  
  console.log('');
  console.log('🎯 RECOMMENDED DEMO APPROACH:');
  console.log('');
  console.log('1. 🎪 TURN THE ERROR INTO A FEATURE:');
  console.log('   "Let me show you our enterprise-grade security in action!"');
  console.log('   "This 429 error demonstrates rate limiting protection"');
  console.log('   "It prevents brute force attacks automatically"');
  console.log('');
  console.log('2. 🔄 RESTART TO CONTINUE:');
  console.log('   "Now let me restart the server to continue the demo"');
  console.log('   Stop server (Ctrl+C) and run: npm run dev');
  console.log('');
  console.log('3. 🎓 COMPLETE THE STUDENT DEMO:');
  console.log('   Login with: <EMAIL> / demo123');
  console.log('   Show the comprehensive student portal');
  console.log('');
  console.log('✨ This approach shows BOTH security AND functionality!');
  
} else {
  console.log('❌ Security file not found. Make sure you\'re in the project root.');
}

console.log('');
console.log('🚀 QUICK DEMO RECOVERY:');
console.log('   1. Explain the security feature (30 seconds)');
console.log('   2. Restart server (30 seconds)');
console.log('   3. Continue with student login (smooth demo)');
console.log('');
console.log('🎉 Your security system is working perfectly!');
console.log('   This is actually a POSITIVE demonstration of your system\'s robustness!');
