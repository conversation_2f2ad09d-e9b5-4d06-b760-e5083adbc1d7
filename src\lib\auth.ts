/**
 * Enhanced Authentication System with JWT
 * Provides secure authentication, session management, and token handling
 */

import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { PrismaClient } from '../generated/prisma';
import { SECURITY_CONFIG } from './security';

const prisma = new PrismaClient();

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-change-in-production';
const JWT_EXPIRES_IN = '24h';
const REFRESH_TOKEN_EXPIRES_IN = '7d';

export interface UserPayload {
  id: string;
  email: string;
  userType: 'o_level_student' | 'a_level_student' | 'teacher' | 'examiner' | 'admin';
  fullName: string;
  schoolId?: string;
  centerNumber?: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface AuthResult {
  success: boolean;
  user?: UserPayload;
  tokens?: TokenPair;
  error?: string;
  requiresVerification?: boolean;
}

// Refresh token store (In production, use Redis or database)
interface RefreshTokenData {
  userId: string;
  userType: string;
  expiresAt: Date;
  isRevoked: boolean;
  deviceInfo?: string;
}

const refreshTokenStore = new Map<string, RefreshTokenData>();

/**
 * Enhanced Authentication Class
 */
export class AuthService {
  /**
   * Generate JWT tokens
   */
  static generateTokens(user: UserPayload, deviceInfo?: string): TokenPair {
    const payload = {
      id: user.id,
      email: user.email,
      userType: user.userType,
      fullName: user.fullName,
      schoolId: user.schoolId,
      centerNumber: user.centerNumber,
      iat: Math.floor(Date.now() / 1000)
    };

    const accessToken = jwt.sign(payload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: 'gce-system',
      audience: 'gce-users'
    });

    const refreshToken = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

    // Store refresh token
    refreshTokenStore.set(refreshToken, {
      userId: user.id,
      userType: user.userType,
      expiresAt,
      isRevoked: false,
      deviceInfo
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: 24 * 60 * 60 // 24 hours in seconds
    };
  }

  /**
   * Verify JWT token
   */
  static verifyToken(token: string): { valid: boolean; payload?: any; error?: string } {
    try {
      const payload = jwt.verify(token, JWT_SECRET, {
        issuer: 'gce-system',
        audience: 'gce-users'
      });
      
      return { valid: true, payload };
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        return { valid: false, error: 'Token expired' };
      } else if (error instanceof jwt.JsonWebTokenError) {
        return { valid: false, error: 'Invalid token' };
      } else {
        return { valid: false, error: 'Token verification failed' };
      }
    }
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshAccessToken(refreshToken: string, deviceInfo?: string): Promise<AuthResult> {
    const tokenData = refreshTokenStore.get(refreshToken);
    
    if (!tokenData) {
      return { success: false, error: 'Invalid refresh token' };
    }

    if (tokenData.isRevoked) {
      return { success: false, error: 'Refresh token has been revoked' };
    }

    if (new Date() > tokenData.expiresAt) {
      refreshTokenStore.delete(refreshToken);
      return { success: false, error: 'Refresh token expired' };
    }

    try {
      // Get user data
      const user = await this.getUserById(tokenData.userId, tokenData.userType);
      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Generate new tokens
      const tokens = this.generateTokens(user, deviceInfo);
      
      // Revoke old refresh token
      refreshTokenStore.delete(refreshToken);

      return {
        success: true,
        user,
        tokens
      };
    } catch (error) {
      return { success: false, error: 'Failed to refresh token' };
    }
  }

  /**
   * Authenticate user with email and password
   */
  static async authenticate(
    email: string, 
    password: string, 
    userType?: string,
    deviceInfo?: string
  ): Promise<AuthResult> {
    try {
      // Find user across all schemas
      const user = await this.findUserByEmail(email, userType);
      
      if (!user) {
        return { success: false, error: 'Invalid credentials' };
      }

      // Verify password
      const passwordValid = await bcrypt.compare(password, user.passwordHash);
      
      if (!passwordValid) {
        // Log failed attempt
        await this.logAuthAttempt(email, false, 'Invalid password');
        return { success: false, error: 'Invalid credentials' };
      }

      // Check if email is verified
      if (!user.emailVerified) {
        return { 
          success: false, 
          error: 'Email not verified',
          requiresVerification: true 
        };
      }

      // Check if account is active
      if (user.registrationStatus !== 'confirmed') {
        return { 
          success: false, 
          error: `Account status: ${user.registrationStatus}` 
        };
      }

      // Create user payload
      const userPayload: UserPayload = {
        id: user.id,
        email: user.email,
        userType: this.getUserTypeFromSchema(user),
        fullName: user.fullName,
        schoolId: user.schoolId,
        centerNumber: user.centerNumber || user.centerCode
      };

      // Generate tokens
      const tokens = this.generateTokens(userPayload, deviceInfo);

      // Update last login
      await this.updateLastLogin(user.id, userPayload.userType);

      // Log successful attempt
      await this.logAuthAttempt(email, true, 'Login successful');

      return {
        success: true,
        user: userPayload,
        tokens
      };

    } catch (error) {
      console.error('Authentication error:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }

  /**
   * Logout user and revoke tokens
   */
  static async logout(refreshToken?: string, accessToken?: string): Promise<{ success: boolean }> {
    try {
      // Revoke refresh token
      if (refreshToken && refreshTokenStore.has(refreshToken)) {
        const tokenData = refreshTokenStore.get(refreshToken)!;
        tokenData.isRevoked = true;
        refreshTokenStore.set(refreshToken, tokenData);
      }

      // In production, add access token to blacklist
      // For now, we rely on short expiration times

      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false };
    }
  }

  /**
   * Find user by email across all schemas
   */
  private static async findUserByEmail(email: string, preferredUserType?: string): Promise<any> {
    const normalizedEmail = email.toLowerCase();

    // If user type is specified, search that schema first
    if (preferredUserType) {
      const user = await this.searchUserInSchema(normalizedEmail, preferredUserType);
      if (user) return user;
    }

    // Search all schemas
    const schemas = ['o_level_student', 'a_level_student', 'teacher', 'examiner'];
    
    for (const schema of schemas) {
      if (schema === preferredUserType) continue; // Already searched
      
      const user = await this.searchUserInSchema(normalizedEmail, schema);
      if (user) return user;
    }

    return null;
  }

  /**
   * Search for user in specific schema
   */
  private static async searchUserInSchema(email: string, userType: string): Promise<any> {
    try {
      switch (userType) {
        case 'o_level_student':
          return await prisma.oLevelStudent.findUnique({ where: { email } });
        case 'a_level_student':
          return await prisma.aLevelStudent.findUnique({ where: { email } });
        case 'teacher':
          return await prisma.teacherUser.findUnique({ where: { email } });
        case 'examiner':
          return await prisma.examinerUser.findUnique({ where: { email } });
        default:
          return null;
      }
    } catch (error) {
      console.error(`Error searching ${userType} schema:`, error);
      return null;
    }
  }

  /**
   * Get user by ID and type
   */
  private static async getUserById(id: string, userType: string): Promise<UserPayload | null> {
    try {
      let user: any = null;

      switch (userType) {
        case 'o_level_student':
          user = await prisma.oLevelStudent.findUnique({ where: { id } });
          break;
        case 'a_level_student':
          user = await prisma.aLevelStudent.findUnique({ where: { id } });
          break;
        case 'teacher':
          user = await prisma.teacherUser.findUnique({ where: { id } });
          break;
        case 'examiner':
          user = await prisma.examinerUser.findUnique({ where: { id } });
          break;
      }

      if (!user) return null;

      return {
        id: user.id,
        email: user.email,
        userType: userType as any,
        fullName: user.fullName,
        schoolId: user.schoolId,
        centerNumber: user.centerNumber || user.centerCode
      };
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  /**
   * Determine user type from user object
   */
  private static getUserTypeFromSchema(user: any): UserPayload['userType'] {
    // This would be determined by which schema the user was found in
    // For now, we'll use a simple heuristic
    if (user.oLevelSubjects) return 'o_level_student';
    if (user.aLevelSubjects) return 'a_level_student';
    if (user.teachingSubjects) return 'teacher';
    if (user.specialization) return 'examiner';
    return 'o_level_student'; // Default
  }

  /**
   * Update last login timestamp
   */
  private static async updateLastLogin(id: string, userType: string): Promise<void> {
    try {
      const now = new Date();
      
      switch (userType) {
        case 'o_level_student':
          await prisma.oLevelStudent.update({ where: { id }, data: { lastLogin: now } });
          break;
        case 'a_level_student':
          await prisma.aLevelStudent.update({ where: { id }, data: { lastLogin: now } });
          break;
        case 'teacher':
          await prisma.teacherUser.update({ where: { id }, data: { lastLogin: now } });
          break;
        case 'examiner':
          await prisma.examinerUser.update({ where: { id }, data: { lastLogin: now } });
          break;
      }
    } catch (error) {
      console.error('Error updating last login:', error);
    }
  }

  /**
   * Log authentication attempt
   */
  private static async logAuthAttempt(email: string, success: boolean, reason: string): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          tableName: 'auth_attempts',
          recordId: crypto.randomUUID(),
          action: success ? 'LOGIN_SUCCESS' : 'LOGIN_FAILED',
          newValues: { email, reason },
          userType: 'system',
          userId: 'auth-system',
          userEmail: email,
          timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('Error logging auth attempt:', error);
    }
  }

  /**
   * Clean up expired refresh tokens
   */
  static cleanupExpiredTokens(): void {
    const now = new Date();
    for (const [token, data] of refreshTokenStore.entries()) {
      if (now > data.expiresAt || data.isRevoked) {
        refreshTokenStore.delete(token);
      }
    }
  }
}

// Clean up expired tokens every hour
setInterval(() => {
  AuthService.cleanupExpiredTokens();
}, 60 * 60 * 1000);

export { AuthService };
