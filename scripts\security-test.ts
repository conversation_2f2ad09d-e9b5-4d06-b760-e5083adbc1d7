#!/usr/bin/env tsx
/**
 * Security Testing Suite
 * Comprehensive security tests for the GCE System
 */

import { AuthService } from '../src/lib/auth';
import { RateLimiter, InputValidator, CSRFProtection } from '../src/lib/security';
import * as fs from 'fs';

interface SecurityTestResult {
  testName: string;
  passed: boolean;
  details: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface SecurityTestSuite {
  suiteName: string;
  results: SecurityTestResult[];
  passed: number;
  failed: number;
  totalTests: number;
}

class SecurityTester {
  private results: SecurityTestSuite[] = [];

  async runAllTests(): Promise<void> {
    console.log('🔒 Starting Security Test Suite...\n');

    await this.testAuthentication();
    await this.testInputValidation();
    await this.testRateLimiting();
    await this.testCSRFProtection();
    await this.testPasswordSecurity();
    await this.testTokenSecurity();
    await this.testSessionManagement();

    this.generateReport();
  }

  private async testAuthentication(): Promise<void> {
    const suite: SecurityTestSuite = {
      suiteName: 'Authentication Tests',
      results: [],
      passed: 0,
      failed: 0,
      totalTests: 0
    };

    // Test 1: Invalid credentials
    try {
      const result = await AuthService.authenticate('<EMAIL>', 'wrongpassword');
      suite.results.push({
        testName: 'Invalid Credentials Rejection',
        passed: !result.success,
        details: result.success ? 'Should reject invalid credentials' : 'Correctly rejected invalid credentials',
        severity: 'high'
      });
    } catch (error) {
      suite.results.push({
        testName: 'Invalid Credentials Rejection',
        passed: false,
        details: `Error during test: ${error}`,
        severity: 'high'
      });
    }

    // Test 2: Empty credentials
    try {
      const result = await AuthService.authenticate('', '');
      suite.results.push({
        testName: 'Empty Credentials Rejection',
        passed: !result.success,
        details: result.success ? 'Should reject empty credentials' : 'Correctly rejected empty credentials',
        severity: 'high'
      });
    } catch (error) {
      suite.results.push({
        testName: 'Empty Credentials Rejection',
        passed: false,
        details: `Error during test: ${error}`,
        severity: 'high'
      });
    }

    // Test 3: SQL Injection attempt
    try {
      const result = await AuthService.authenticate("admin'; DROP TABLE users; --", 'password');
      suite.results.push({
        testName: 'SQL Injection Protection',
        passed: !result.success,
        details: result.success ? 'Vulnerable to SQL injection' : 'Protected against SQL injection',
        severity: 'critical'
      });
    } catch (error) {
      suite.results.push({
        testName: 'SQL Injection Protection',
        passed: true,
        details: 'SQL injection attempt properly handled',
        severity: 'critical'
      });
    }

    // Test 4: Token verification
    try {
      const invalidToken = 'invalid.jwt.token';
      const verification = AuthService.verifyToken(invalidToken);
      suite.results.push({
        testName: 'Invalid Token Rejection',
        passed: !verification.valid,
        details: verification.valid ? 'Should reject invalid tokens' : 'Correctly rejected invalid token',
        severity: 'high'
      });
    } catch (error) {
      suite.results.push({
        testName: 'Invalid Token Rejection',
        passed: true,
        details: 'Invalid token properly rejected',
        severity: 'high'
      });
    }

    this.calculateSuiteStats(suite);
    this.results.push(suite);
  }

  private async testInputValidation(): Promise<void> {
    const suite: SecurityTestSuite = {
      suiteName: 'Input Validation Tests',
      results: [],
      passed: 0,
      failed: 0,
      totalTests: 0
    };

    // Test 1: Email validation
    const emailTests = [
      { email: '<EMAIL>', shouldPass: true },
      { email: 'invalid-email', shouldPass: false },
      { email: '<script>alert("xss")</script>@test.com', shouldPass: false },
      { email: '', shouldPass: false },
      { email: 'a'.repeat(300) + '@test.com', shouldPass: false }
    ];

    emailTests.forEach((test, index) => {
      const result = InputValidator.validateEmail(test.email);
      suite.results.push({
        testName: `Email Validation Test ${index + 1}`,
        passed: result.valid === test.shouldPass,
        details: `Email: ${test.email.substring(0, 50)}... Expected: ${test.shouldPass}, Got: ${result.valid}`,
        severity: 'medium'
      });
    });

    // Test 2: Password validation
    const passwordTests = [
      { password: 'StrongP@ss123', shouldPass: true },
      { password: 'weak', shouldPass: false },
      { password: 'NoNumbers!', shouldPass: false },
      { password: 'nonumbers123', shouldPass: false },
      { password: 'NOLOWERCASE123!', shouldPass: false },
      { password: '', shouldPass: false }
    ];

    passwordTests.forEach((test, index) => {
      const result = InputValidator.validatePassword(test.password);
      suite.results.push({
        testName: `Password Validation Test ${index + 1}`,
        passed: result.valid === test.shouldPass,
        details: `Password strength check. Expected: ${test.shouldPass}, Got: ${result.valid}`,
        severity: 'high'
      });
    });

    // Test 3: XSS protection
    const xssTests = [
      '<script>alert("xss")</script>',
      '<img src="x" onerror="alert(1)">',
      'javascript:alert(1)',
      '<iframe src="javascript:alert(1)"></iframe>'
    ];

    xssTests.forEach((xssPayload, index) => {
      const sanitized = InputValidator.sanitizeString(xssPayload);
      const containsScript = sanitized.toLowerCase().includes('<script') || 
                           sanitized.toLowerCase().includes('javascript:') ||
                           sanitized.toLowerCase().includes('onerror');
      
      suite.results.push({
        testName: `XSS Protection Test ${index + 1}`,
        passed: !containsScript,
        details: `XSS payload sanitization. Original: ${xssPayload.substring(0, 30)}...`,
        severity: 'critical'
      });
    });

    this.calculateSuiteStats(suite);
    this.results.push(suite);
  }

  private async testRateLimiting(): Promise<void> {
    const suite: SecurityTestSuite = {
      suiteName: 'Rate Limiting Tests',
      results: [],
      passed: 0,
      failed: 0,
      totalTests: 0
    };

    const testIP = '*************';
    const testEndpoint = '/api/test';

    // Test 1: Normal requests should pass
    try {
      const result = await RateLimiter.checkRateLimit(testIP, testEndpoint, { windowMs: 60000, maxRequests: 5 });
      suite.results.push({
        testName: 'Normal Request Rate Limit',
        passed: result.allowed,
        details: `First request should be allowed. Remaining: ${result.remaining}`,
        severity: 'medium'
      });
    } catch (error) {
      suite.results.push({
        testName: 'Normal Request Rate Limit',
        passed: false,
        details: `Error during test: ${error}`,
        severity: 'medium'
      });
    }

    // Test 2: Excessive requests should be blocked
    try {
      let lastResult;
      // Make 6 requests (limit is 5)
      for (let i = 0; i < 6; i++) {
        lastResult = await RateLimiter.checkRateLimit(testIP + '_burst', testEndpoint, { windowMs: 60000, maxRequests: 5 });
      }
      
      suite.results.push({
        testName: 'Rate Limit Enforcement',
        passed: !lastResult!.allowed,
        details: `6th request should be blocked. Was allowed: ${lastResult!.allowed}`,
        severity: 'high'
      });
    } catch (error) {
      suite.results.push({
        testName: 'Rate Limit Enforcement',
        passed: false,
        details: `Error during test: ${error}`,
        severity: 'high'
      });
    }

    this.calculateSuiteStats(suite);
    this.results.push(suite);
  }

  private async testCSRFProtection(): Promise<void> {
    const suite: SecurityTestSuite = {
      suiteName: 'CSRF Protection Tests',
      results: [],
      passed: 0,
      failed: 0,
      totalTests: 0
    };

    const sessionId = 'test-session-123';

    // Test 1: Valid CSRF token
    try {
      const token = CSRFProtection.generateToken(sessionId);
      const isValid = CSRFProtection.validateToken(sessionId, token);
      
      suite.results.push({
        testName: 'Valid CSRF Token',
        passed: isValid,
        details: `Valid token should be accepted. Result: ${isValid}`,
        severity: 'high'
      });
    } catch (error) {
      suite.results.push({
        testName: 'Valid CSRF Token',
        passed: false,
        details: `Error during test: ${error}`,
        severity: 'high'
      });
    }

    // Test 2: Invalid CSRF token
    try {
      const invalidToken = 'invalid-csrf-token';
      const isValid = CSRFProtection.validateToken(sessionId, invalidToken);
      
      suite.results.push({
        testName: 'Invalid CSRF Token Rejection',
        passed: !isValid,
        details: `Invalid token should be rejected. Result: ${isValid}`,
        severity: 'high'
      });
    } catch (error) {
      suite.results.push({
        testName: 'Invalid CSRF Token Rejection',
        passed: true,
        details: 'Invalid token properly rejected',
        severity: 'high'
      });
    }

    // Test 3: Token reuse prevention
    try {
      const token = CSRFProtection.generateToken(sessionId + '_reuse');
      const firstUse = CSRFProtection.validateToken(sessionId + '_reuse', token);
      const secondUse = CSRFProtection.validateToken(sessionId + '_reuse', token);
      
      suite.results.push({
        testName: 'CSRF Token Reuse Prevention',
        passed: firstUse && !secondUse,
        details: `Token should work once only. First: ${firstUse}, Second: ${secondUse}`,
        severity: 'high'
      });
    } catch (error) {
      suite.results.push({
        testName: 'CSRF Token Reuse Prevention',
        passed: false,
        details: `Error during test: ${error}`,
        severity: 'high'
      });
    }

    this.calculateSuiteStats(suite);
    this.results.push(suite);
  }

  private async testPasswordSecurity(): Promise<void> {
    const suite: SecurityTestSuite = {
      suiteName: 'Password Security Tests',
      results: [],
      passed: 0,
      failed: 0,
      totalTests: 0
    };

    // Test 1: Password complexity requirements
    const weakPasswords = ['123456', 'password', 'qwerty', 'admin', ''];
    let weakPasswordsRejected = 0;

    weakPasswords.forEach(password => {
      const result = InputValidator.validatePassword(password);
      if (!result.valid) weakPasswordsRejected++;
    });

    suite.results.push({
      testName: 'Weak Password Rejection',
      passed: weakPasswordsRejected === weakPasswords.length,
      details: `${weakPasswordsRejected}/${weakPasswords.length} weak passwords rejected`,
      severity: 'high'
    });

    // Test 2: Password length limits
    const tooLongPassword = 'a'.repeat(200);
    const lengthResult = InputValidator.validatePassword(tooLongPassword);
    
    suite.results.push({
      testName: 'Password Length Limit',
      passed: !lengthResult.valid,
      details: `Overly long password should be rejected. Result: ${lengthResult.valid}`,
      severity: 'medium'
    });

    this.calculateSuiteStats(suite);
    this.results.push(suite);
  }

  private async testTokenSecurity(): Promise<void> {
    const suite: SecurityTestSuite = {
      suiteName: 'Token Security Tests',
      results: [],
      passed: 0,
      failed: 0,
      totalTests: 0
    };

    // Test 1: Token format validation
    const invalidTokens = [
      'invalid-token',
      '',
      'a.b',
      'header.payload', // Missing signature
      'not.a.jwt.token.at.all'
    ];

    let invalidTokensRejected = 0;
    invalidTokens.forEach(token => {
      const result = AuthService.verifyToken(token);
      if (!result.valid) invalidTokensRejected++;
    });

    suite.results.push({
      testName: 'Invalid Token Format Rejection',
      passed: invalidTokensRejected === invalidTokens.length,
      details: `${invalidTokensRejected}/${invalidTokens.length} invalid tokens rejected`,
      severity: 'high'
    });

    this.calculateSuiteStats(suite);
    this.results.push(suite);
  }

  private async testSessionManagement(): Promise<void> {
    const suite: SecurityTestSuite = {
      suiteName: 'Session Management Tests',
      results: [],
      passed: 0,
      failed: 0,
      totalTests: 0
    };

    // Test 1: Session cleanup
    try {
      AuthService.cleanupExpiredTokens();
      suite.results.push({
        testName: 'Session Cleanup',
        passed: true,
        details: 'Session cleanup executed without errors',
        severity: 'low'
      });
    } catch (error) {
      suite.results.push({
        testName: 'Session Cleanup',
        passed: false,
        details: `Session cleanup failed: ${error}`,
        severity: 'medium'
      });
    }

    this.calculateSuiteStats(suite);
    this.results.push(suite);
  }

  private calculateSuiteStats(suite: SecurityTestSuite): void {
    suite.totalTests = suite.results.length;
    suite.passed = suite.results.filter(r => r.passed).length;
    suite.failed = suite.totalTests - suite.passed;
  }

  private generateReport(): void {
    console.log('\n🔒 Security Test Results');
    console.log('========================\n');

    let totalTests = 0;
    let totalPassed = 0;
    let criticalFailures = 0;
    let highFailures = 0;

    this.results.forEach(suite => {
      console.log(`📋 ${suite.suiteName}`);
      console.log(`   ✅ Passed: ${suite.passed}`);
      console.log(`   ❌ Failed: ${suite.failed}`);
      console.log(`   📊 Total: ${suite.totalTests}\n`);

      totalTests += suite.totalTests;
      totalPassed += suite.passed;

      // Count failures by severity
      suite.results.forEach(result => {
        if (!result.passed) {
          if (result.severity === 'critical') criticalFailures++;
          if (result.severity === 'high') highFailures++;
        }
      });

      // Show failed tests
      const failedTests = suite.results.filter(r => !r.passed);
      if (failedTests.length > 0) {
        console.log(`   ⚠️  Failed Tests:`);
        failedTests.forEach(test => {
          const severityIcon = {
            low: '🟡',
            medium: '🟠', 
            high: '🔴',
            critical: '🚨'
          }[test.severity];
          
          console.log(`      ${severityIcon} ${test.testName}: ${test.details}`);
        });
        console.log('');
      }
    });

    // Overall summary
    const passRate = ((totalPassed / totalTests) * 100).toFixed(1);
    console.log(`📊 Overall Results:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${totalPassed}`);
    console.log(`   Failed: ${totalTests - totalPassed}`);
    console.log(`   Pass Rate: ${passRate}%`);
    
    if (criticalFailures > 0) {
      console.log(`   🚨 Critical Failures: ${criticalFailures}`);
    }
    if (highFailures > 0) {
      console.log(`   🔴 High Severity Failures: ${highFailures}`);
    }

    // Security recommendations
    console.log('\n💡 Security Recommendations:');
    if (criticalFailures > 0) {
      console.log('   🚨 URGENT: Address critical security failures immediately');
    }
    if (highFailures > 0) {
      console.log('   🔴 HIGH: Address high severity failures before production');
    }
    if (passRate === '100.0') {
      console.log('   ✅ All security tests passed! System appears secure.');
    } else {
      console.log('   ⚠️  Some security tests failed. Review and fix before deployment.');
    }

    // Save detailed report
    const reportPath = `./security-test-report-${new Date().toISOString().split('T')[0]}.json`;
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: {
        totalTests,
        totalPassed,
        totalFailed: totalTests - totalPassed,
        passRate: parseFloat(passRate),
        criticalFailures,
        highFailures
      },
      suites: this.results
    }, null, 2));

    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  }
}

async function main() {
  const tester = new SecurityTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main();
}

export { SecurityTester };
