/**
 * Comprehensive Monitoring and Analytics System
 * Tracks performance, errors, user behavior, and system health
 */

import { PrismaClient } from '../generated/prisma';
import { RedisManager } from './redis';

const prisma = new PrismaClient();

interface MetricData {
  name: string;
  value: number;
  unit: string;
  tags?: Record<string, string>;
  timestamp: Date;
}

interface ErrorData {
  message: string;
  stack?: string;
  code?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
  timestamp: Date;
}

interface UserEvent {
  userId?: string;
  sessionId: string;
  event: string;
  properties?: Record<string, any>;
  timestamp: Date;
}

interface SystemHealth {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  error?: string;
  timestamp: Date;
}

/**
 * Performance Metrics Collector
 */
export class MetricsCollector {
  private static metrics: MetricData[] = [];
  private static maxMetrics = 10000;

  /**
   * Record a metric
   */
  static record(
    name: string,
    value: number,
    unit: string = 'count',
    tags?: Record<string, string>
  ): void {
    const metric: MetricData = {
      name,
      value,
      unit,
      tags,
      timestamp: new Date()
    };

    this.metrics.push(metric);

    // Keep only recent metrics in memory
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Store in Redis for distributed access
    this.storeInRedis(metric);

    // Log critical metrics
    if (this.isCriticalMetric(name, value)) {
      console.warn(`🚨 Critical metric: ${name} = ${value} ${unit}`, tags);
    }
  }

  /**
   * Get metrics by name and time range
   */
  static getMetrics(
    name?: string,
    timeRange?: { start: Date; end: Date },
    tags?: Record<string, string>
  ): MetricData[] {
    let filtered = this.metrics;

    if (name) {
      filtered = filtered.filter(m => m.name === name);
    }

    if (timeRange) {
      filtered = filtered.filter(m => 
        m.timestamp >= timeRange.start && m.timestamp <= timeRange.end
      );
    }

    if (tags) {
      filtered = filtered.filter(m => {
        if (!m.tags) return false;
        return Object.entries(tags).every(([key, value]) => m.tags![key] === value);
      });
    }

    return filtered;
  }

  /**
   * Get aggregated statistics
   */
  static getStats(name: string, timeRange?: { start: Date; end: Date }): {
    count: number;
    sum: number;
    average: number;
    min: number;
    max: number;
    p50: number;
    p95: number;
    p99: number;
  } {
    const metrics = this.getMetrics(name, timeRange);
    
    if (metrics.length === 0) {
      return { count: 0, sum: 0, average: 0, min: 0, max: 0, p50: 0, p95: 0, p99: 0 };
    }

    const values = metrics.map(m => m.value).sort((a, b) => a - b);
    const count = values.length;
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      count,
      sum,
      average: sum / count,
      min: values[0],
      max: values[count - 1],
      p50: values[Math.floor(count * 0.5)],
      p95: values[Math.floor(count * 0.95)],
      p99: values[Math.floor(count * 0.99)]
    };
  }

  /**
   * Store metric in Redis for persistence
   */
  private static async storeInRedis(metric: MetricData): Promise<void> {
    try {
      const key = `metrics:${metric.name}:${Date.now()}`;
      await RedisManager.set(key, metric, { ttl: 7 * 24 * 60 * 60 }); // 7 days
    } catch (error) {
      // Silently fail if Redis is not available
    }
  }

  /**
   * Check if metric is critical
   */
  private static isCriticalMetric(name: string, value: number): boolean {
    const criticalThresholds: Record<string, number> = {
      'api.response_time': 5000, // 5 seconds
      'database.query_time': 10000, // 10 seconds
      'error.rate': 0.05, // 5% error rate
      'memory.usage': 0.9, // 90% memory usage
      'cpu.usage': 0.8 // 80% CPU usage
    };

    return criticalThresholds[name] !== undefined && value > criticalThresholds[name];
  }
}

/**
 * Error Tracking System
 */
export class ErrorTracker {
  private static errors: ErrorData[] = [];
  private static maxErrors = 1000;

  /**
   * Track an error
   */
  static track(
    error: Error | string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    context?: Record<string, any>
  ): void {
    const errorData: ErrorData = {
      message: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      severity,
      context,
      timestamp: new Date()
    };

    this.errors.push(errorData);

    // Keep only recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors);
    }

    // Log to console based on severity
    const logMethod = severity === 'critical' ? console.error : 
                     severity === 'high' ? console.error :
                     severity === 'medium' ? console.warn : console.log;

    logMethod(`🚨 [${severity.toUpperCase()}] ${errorData.message}`, context);

    // Store in database for persistence
    this.storeInDatabase(errorData);

    // Send alerts for critical errors
    if (severity === 'critical') {
      this.sendAlert(errorData);
    }
  }

  /**
   * Get errors by severity and time range
   */
  static getErrors(
    severity?: 'low' | 'medium' | 'high' | 'critical',
    timeRange?: { start: Date; end: Date }
  ): ErrorData[] {
    let filtered = this.errors;

    if (severity) {
      filtered = filtered.filter(e => e.severity === severity);
    }

    if (timeRange) {
      filtered = filtered.filter(e => 
        e.timestamp >= timeRange.start && e.timestamp <= timeRange.end
      );
    }

    return filtered;
  }

  /**
   * Get error statistics
   */
  static getErrorStats(timeRange?: { start: Date; end: Date }): {
    total: number;
    bySeverity: Record<string, number>;
    topErrors: Array<{ message: string; count: number }>;
  } {
    const errors = this.getErrors(undefined, timeRange);
    
    const bySeverity = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0
    };

    const errorCounts: Record<string, number> = {};

    errors.forEach(error => {
      bySeverity[error.severity]++;
      errorCounts[error.message] = (errorCounts[error.message] || 0) + 1;
    });

    const topErrors = Object.entries(errorCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([message, count]) => ({ message, count }));

    return {
      total: errors.length,
      bySeverity,
      topErrors
    };
  }

  /**
   * Store error in database
   */
  private static async storeInDatabase(error: ErrorData): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          tableName: 'errors',
          recordId: `error-${Date.now()}`,
          action: 'ERROR_TRACKED',
          newValues: {
            message: error.message,
            severity: error.severity,
            context: error.context
          },
          userType: 'system',
          userId: 'error-tracker',
          userEmail: '<EMAIL>',
          timestamp: error.timestamp
        }
      });
    } catch (dbError) {
      console.error('Failed to store error in database:', dbError);
    }
  }

  /**
   * Send alert for critical errors
   */
  private static async sendAlert(error: ErrorData): Promise<void> {
    // In production, integrate with alerting service (Slack, PagerDuty, etc.)
    console.error('🚨 CRITICAL ERROR ALERT:', {
      message: error.message,
      timestamp: error.timestamp,
      context: error.context
    });
  }
}

/**
 * User Analytics Tracker
 */
export class UserAnalytics {
  private static events: UserEvent[] = [];
  private static maxEvents = 5000;

  /**
   * Track user event
   */
  static track(
    event: string,
    properties?: Record<string, any>,
    userId?: string,
    sessionId?: string
  ): void {
    const userEvent: UserEvent = {
      userId,
      sessionId: sessionId || 'anonymous',
      event,
      properties,
      timestamp: new Date()
    };

    this.events.push(userEvent);

    // Keep only recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Store in Redis for real-time analytics
    this.storeInRedis(userEvent);
  }

  /**
   * Get events by user or event type
   */
  static getEvents(
    userId?: string,
    event?: string,
    timeRange?: { start: Date; end: Date }
  ): UserEvent[] {
    let filtered = this.events;

    if (userId) {
      filtered = filtered.filter(e => e.userId === userId);
    }

    if (event) {
      filtered = filtered.filter(e => e.event === event);
    }

    if (timeRange) {
      filtered = filtered.filter(e => 
        e.timestamp >= timeRange.start && e.timestamp <= timeRange.end
      );
    }

    return filtered;
  }

  /**
   * Get user analytics summary
   */
  static getAnalytics(timeRange?: { start: Date; end: Date }): {
    totalEvents: number;
    uniqueUsers: number;
    topEvents: Array<{ event: string; count: number }>;
    userActivity: Array<{ hour: number; count: number }>;
  } {
    const events = this.getEvents(undefined, undefined, timeRange);
    
    const uniqueUsers = new Set(events.filter(e => e.userId).map(e => e.userId)).size;
    
    const eventCounts: Record<string, number> = {};
    const hourlyActivity: Record<number, number> = {};

    events.forEach(event => {
      eventCounts[event.event] = (eventCounts[event.event] || 0) + 1;
      
      const hour = event.timestamp.getHours();
      hourlyActivity[hour] = (hourlyActivity[hour] || 0) + 1;
    });

    const topEvents = Object.entries(eventCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([event, count]) => ({ event, count }));

    const userActivity = Array.from({ length: 24 }, (_, hour) => ({
      hour,
      count: hourlyActivity[hour] || 0
    }));

    return {
      totalEvents: events.length,
      uniqueUsers,
      topEvents,
      userActivity
    };
  }

  /**
   * Store event in Redis
   */
  private static async storeInRedis(event: UserEvent): Promise<void> {
    try {
      const key = `analytics:${event.event}:${Date.now()}`;
      await RedisManager.set(key, event, { ttl: 30 * 24 * 60 * 60 }); // 30 days
    } catch (error) {
      // Silently fail if Redis is not available
    }
  }
}

/**
 * System Health Monitor
 */
export class HealthMonitor {
  private static healthChecks: SystemHealth[] = [];
  private static maxChecks = 1000;

  /**
   * Record health check
   */
  static record(
    service: string,
    status: 'healthy' | 'degraded' | 'unhealthy',
    responseTime: number,
    error?: string
  ): void {
    const health: SystemHealth = {
      service,
      status,
      responseTime,
      error,
      timestamp: new Date()
    };

    this.healthChecks.push(health);

    // Keep only recent checks
    if (this.healthChecks.length > this.maxChecks) {
      this.healthChecks = this.healthChecks.slice(-this.maxChecks);
    }

    // Alert on unhealthy services
    if (status === 'unhealthy') {
      console.error(`🚨 Service unhealthy: ${service}`, { responseTime, error });
    }
  }

  /**
   * Get current system health
   */
  static getCurrentHealth(): Record<string, {
    status: 'healthy' | 'degraded' | 'unhealthy';
    responseTime: number;
    lastCheck: Date;
    error?: string;
  }> {
    const latest: Record<string, SystemHealth> = {};

    // Get latest check for each service
    this.healthChecks.forEach(check => {
      if (!latest[check.service] || check.timestamp > latest[check.service].timestamp) {
        latest[check.service] = check;
      }
    });

    const result: Record<string, any> = {};
    Object.entries(latest).forEach(([service, health]) => {
      result[service] = {
        status: health.status,
        responseTime: health.responseTime,
        lastCheck: health.timestamp,
        error: health.error
      };
    });

    return result;
  }

  /**
   * Get health statistics
   */
  static getHealthStats(service?: string, timeRange?: { start: Date; end: Date }): {
    uptime: number;
    averageResponseTime: number;
    totalChecks: number;
    statusDistribution: Record<string, number>;
  } {
    let checks = this.healthChecks;

    if (service) {
      checks = checks.filter(c => c.service === service);
    }

    if (timeRange) {
      checks = checks.filter(c => 
        c.timestamp >= timeRange.start && c.timestamp <= timeRange.end
      );
    }

    if (checks.length === 0) {
      return { uptime: 0, averageResponseTime: 0, totalChecks: 0, statusDistribution: {} };
    }

    const healthyChecks = checks.filter(c => c.status === 'healthy').length;
    const uptime = healthyChecks / checks.length;
    
    const totalResponseTime = checks.reduce((sum, c) => sum + c.responseTime, 0);
    const averageResponseTime = totalResponseTime / checks.length;

    const statusDistribution: Record<string, number> = {};
    checks.forEach(check => {
      statusDistribution[check.status] = (statusDistribution[check.status] || 0) + 1;
    });

    return {
      uptime: Math.round(uptime * 100) / 100,
      averageResponseTime: Math.round(averageResponseTime),
      totalChecks: checks.length,
      statusDistribution
    };
  }
}

/**
 * Monitoring Dashboard Data Provider
 */
export class MonitoringDashboard {
  /**
   * Get comprehensive system overview
   */
  static async getSystemOverview(): Promise<{
    metrics: any;
    errors: any;
    analytics: any;
    health: any;
    timestamp: Date;
  }> {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    return {
      metrics: {
        apiResponseTime: MetricsCollector.getStats('api.response_time', { start: oneHourAgo, end: now }),
        databaseQueryTime: MetricsCollector.getStats('database.query_time', { start: oneHourAgo, end: now }),
        requestCount: MetricsCollector.getStats('api.requests', { start: oneDayAgo, end: now })
      },
      errors: ErrorTracker.getErrorStats({ start: oneDayAgo, end: now }),
      analytics: UserAnalytics.getAnalytics({ start: oneDayAgo, end: now }),
      health: HealthMonitor.getCurrentHealth(),
      timestamp: now
    };
  }
}

export {
  MetricsCollector,
  ErrorTracker,
  UserAnalytics,
  HealthMonitor,
  MonitoringDashboard
};
