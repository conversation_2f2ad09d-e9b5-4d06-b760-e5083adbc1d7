import { NextRequest, NextResponse } from 'next/server';
import { userStorage } from '@/lib/userStorage';
import { prisma } from '@/lib/postgresDb';
import SeparateStudentDatabase from '@/lib/separateStudentDb';

// POST /api/auth/detect-user-type - Detect user type from email
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json(
        { success: false, message: 'Email is required' },
        { status: 400 }
      );
    }

    const emailLower = email.toLowerCase();
    const userTypes: string[] = [];
    const accountInfo: any = {};

    // Check student databases first
    const studentResult = await SeparateStudentDatabase.findStudentByEmail(emailLower);
    if (studentResult) {
      userTypes.push('student');
      accountInfo.student = {
        examLevel: studentResult.examLevel,
        status: 'active'
      };
    }

    // Check other user types in main database
    try {
      const user = await userStorage.findByEmail(emailLower);
      if (user && !userTypes.includes(user.userType)) {
        userTypes.push(user.userType);
        accountInfo[user.userType] = {
          status: user.registrationStatus,
          name: user.fullName
        };
      }
    } catch (error) {
      // User not found in main database - that's okay
    }

    // Check professional registration applications
    const professionalApplications = await prisma.professionalRegistration.findMany({
      where: { email: emailLower },
      select: {
        applicantType: true,
        status: true,
        accountCreated: true,
        submittedAt: true,
        approvedAt: true
      }
    });

    for (const app of professionalApplications) {
      if (!userTypes.includes(app.applicantType)) {
        userTypes.push(app.applicantType);
        accountInfo[app.applicantType] = {
          status: app.status,
          accountCreated: app.accountCreated,
          submittedAt: app.submittedAt,
          approvedAt: app.approvedAt
        };
      }
    }

    // If no user types found, this might be a new user
    if (userTypes.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          userTypes: [],
          isNewUser: true,
          message: 'No existing account found. You may need to register first.'
        }
      });
    }

    // Determine primary user type and status
    let primaryUserType = userTypes[0];
    let accountStatus = 'unknown';
    let canLogin = false;
    let statusMessage = '';

    // Prioritize active accounts
    for (const type of userTypes) {
      const info = accountInfo[type];
      if (type === 'student' && info.status === 'active') {
        primaryUserType = type;
        accountStatus = 'active';
        canLogin = true;
        statusMessage = 'Student account ready for login';
        break;
      } else if ((type === 'teacher' || type === 'examiner') && info.status === 'confirmed') {
        primaryUserType = type;
        accountStatus = 'active';
        canLogin = true;
        statusMessage = `${type.charAt(0).toUpperCase() + type.slice(1)} account ready for login`;
        break;
      } else if ((type === 'teacher' || type === 'examiner') && info.status === 'approved' && info.accountCreated) {
        primaryUserType = type;
        accountStatus = 'active';
        canLogin = true;
        statusMessage = `${type.charAt(0).toUpperCase() + type.slice(1)} account ready for login`;
        break;
      }
    }

    // If no active account, check for pending applications
    if (!canLogin) {
      for (const type of userTypes) {
        const info = accountInfo[type];
        if ((type === 'teacher' || type === 'examiner') && ['pending', 'under_review'].includes(info.status)) {
          primaryUserType = type;
          accountStatus = info.status;
          statusMessage = `${type.charAt(0).toUpperCase() + type.slice(1)} application is ${info.status}`;
          break;
        }
      }
    }

    // Security: Filter sensitive user types for response
    const publicUserTypes = userTypes.filter(type => 
      ['student', 'teacher', 'examiner'].includes(type)
    );

    return NextResponse.json({
      success: true,
      data: {
        userTypes: publicUserTypes,
        primaryUserType,
        accountStatus,
        canLogin,
        statusMessage,
        hasMultipleAccounts: publicUserTypes.length > 1,
        accountInfo: Object.fromEntries(
          Object.entries(accountInfo).filter(([key]) => 
            ['student', 'teacher', 'examiner'].includes(key)
          )
        )
      }
    });

  } catch (error) {
    console.error('User type detection error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
