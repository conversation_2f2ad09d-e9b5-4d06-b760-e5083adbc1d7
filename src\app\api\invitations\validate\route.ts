import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/postgresDb';
import { validateInvitationCodeFormat, logRegistrationAttempt, REGISTRATION_MESSAGES } from '@/lib/secureRegistration';

// POST /api/invitations/validate - Validate an invitation code
export async function POST(request: NextRequest) {
  try {
    // Get IP and user agent for security logging
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || '';

    const body = await request.json();
    const { code } = body;

    if (!code) {
      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'invitation_validation',
        false,
        'Missing invitation code'
      );

      return NextResponse.json(
        { success: false, message: 'Invitation code is required' },
        { status: 400 }
      );
    }

    // Validate code format
    if (!validateInvitationCodeFormat(code)) {
      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'invitation_validation',
        false,
        'Invalid invitation code format'
      );

      return NextResponse.json(
        { success: false, message: REGISTRATION_MESSAGES.INVALID_INVITATION },
        { status: 400 }
      );
    }

    // Find invitation in database
    const invitation = await prisma.registrationInvitation.findUnique({
      where: { code },
      include: {
        // We'll add relations when we update the schema
      }
    });

    if (!invitation) {
      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'invitation_validation',
        false,
        'Invitation code not found'
      );

      return NextResponse.json(
        { success: false, message: REGISTRATION_MESSAGES.INVALID_INVITATION },
        { status: 404 }
      );
    }

    // Check if invitation is expired
    if (invitation.expiresAt < new Date()) {
      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'invitation_validation',
        false,
        'Invitation code expired'
      );

      return NextResponse.json(
        { success: false, message: REGISTRATION_MESSAGES.INVITATION_EXPIRED },
        { status: 400 }
      );
    }

    // Check if invitation is already used
    if (invitation.isUsed) {
      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'invitation_validation',
        false,
        'Invitation code already used'
      );

      return NextResponse.json(
        { success: false, message: REGISTRATION_MESSAGES.INVITATION_USED },
        { status: 400 }
      );
    }

    // Get school information
    const school = await prisma.school.findUnique({
      where: { id: invitation.schoolId },
      select: {
        name: true,
        centerNumber: true,
        region: true
      }
    });

    // Get pre-registration details
    const preRegistration = await prisma.schoolPreRegistration.findUnique({
      where: { id: invitation.preRegistrationId },
      select: {
        studentName: true,
        nationalId: true,
        dateOfBirth: true,
        gender: true,
        academicYear: true
      }
    });

    // Log successful validation
    await logRegistrationAttempt(
      ipAddress,
      userAgent,
      'invitation_validation',
      true
    );

    // Return invitation details
    return NextResponse.json({
      success: true,
      data: {
        valid: true,
        code: invitation.code,
        studentName: invitation.studentName,
        examLevel: invitation.examLevel,
        expiresAt: invitation.expiresAt,
        schoolName: school?.name || 'Unknown School',
        schoolCenterNumber: school?.centerNumber,
        schoolRegion: school?.region,
        preRegistrationDetails: preRegistration ? {
          nationalId: preRegistration.nationalId,
          dateOfBirth: preRegistration.dateOfBirth,
          gender: preRegistration.gender,
          academicYear: preRegistration.academicYear
        } : null
      },
      message: 'Invitation code is valid'
    });

  } catch (error) {
    console.error('Invitation validation error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/invitations/validate - Check invitation status (for monitoring)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');

    if (!code) {
      return NextResponse.json(
        { success: false, message: 'Invitation code is required' },
        { status: 400 }
      );
    }

    // Find invitation
    const invitation = await prisma.registrationInvitation.findUnique({
      where: { code },
      select: {
        id: true,
        code: true,
        studentName: true,
        examLevel: true,
        expiresAt: true,
        isUsed: true,
        usedAt: true,
        createdAt: true
      }
    });

    if (!invitation) {
      return NextResponse.json(
        { success: false, message: 'Invitation not found' },
        { status: 404 }
      );
    }

    // Determine status
    let status: string;
    if (invitation.isUsed) {
      status = 'used';
    } else if (invitation.expiresAt < new Date()) {
      status = 'expired';
    } else {
      status = 'active';
    }

    return NextResponse.json({
      success: true,
      data: {
        ...invitation,
        status
      },
      message: 'Invitation status retrieved'
    });

  } catch (error) {
    console.error('Invitation status check error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
