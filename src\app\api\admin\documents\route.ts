import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/postgresDb';

// GET /api/admin/documents - Get documents for verification
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const registrationId = searchParams.get('registrationId');
    const status = searchParams.get('status') || 'pending';

    if (registrationId) {
      // Get documents for specific registration
      const documents = await prisma.documentVerification.findMany({
        where: { registrationId },
        orderBy: { uploadedAt: 'desc' }
      });

      return NextResponse.json({
        success: true,
        data: documents,
        message: 'Documents retrieved successfully'
      });
    } else {
      // Get all documents pending verification
      const documents = await prisma.documentVerification.findMany({
        where: { verificationStatus: status },
        orderBy: { uploadedAt: 'desc' },
        include: {
          // Include registration details if available
        }
      });

      return NextResponse.json({
        success: true,
        data: documents,
        message: 'Documents retrieved successfully'
      });
    }

  } catch (error) {
    console.error('Get documents error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/documents - Verify or reject documents
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      documentId,
      action, // 'approve', 'reject', 'request_resubmission'
      adminId,
      adminName,
      verificationNotes,
      rejectionReason
    } = body;

    if (!documentId || !action || !adminId) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    const document = await prisma.documentVerification.findUnique({
      where: { id: documentId }
    });

    if (!document) {
      return NextResponse.json(
        { success: false, message: 'Document not found' },
        { status: 404 }
      );
    }

    let updateData: any = {
      verifiedBy: adminName,
      verifiedAt: new Date(),
      verificationNotes
    };

    switch (action) {
      case 'approve':
        updateData.verificationStatus = 'verified';
        updateData.isVerified = true;
        break;
      case 'reject':
        updateData.verificationStatus = 'rejected';
        updateData.isVerified = false;
        updateData.rejectionReason = rejectionReason;
        break;
      case 'request_resubmission':
        updateData.verificationStatus = 'resubmission_required';
        updateData.isVerified = false;
        updateData.resubmissionReason = rejectionReason;
        break;
      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }

    const updatedDocument = await prisma.documentVerification.update({
      where: { id: documentId },
      data: updateData
    });

    // Create audit log
    await prisma.auditLog.create({
      data: {
        tableName: 'document_verification',
        recordId: documentId,
        action: action.toUpperCase(),
        oldValues: { 
          verificationStatus: document.verificationStatus,
          isVerified: document.isVerified 
        },
        newValues: {
          verificationStatus: updateData.verificationStatus,
          isVerified: updateData.isVerified,
          verifiedBy: adminName
        },
        userType: 'admin',
        userId: adminId,
        userEmail: adminName,
        timestamp: new Date()
      }
    });

    // Check if all documents for registration are verified
    if (action === 'approve') {
      await checkRegistrationDocumentCompletion(document.registrationId);
    }

    return NextResponse.json({
      success: true,
      data: updatedDocument,
      message: `Document ${action}d successfully`
    });

  } catch (error) {
    console.error('Document verification error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/documents - Bulk document verification
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      documentIds,
      action,
      adminId,
      adminName,
      verificationNotes,
      rejectionReason
    } = body;

    if (!documentIds || !Array.isArray(documentIds) || !action || !adminId) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    const results = [];

    for (const docId of documentIds) {
      try {
        const document = await prisma.documentVerification.findUnique({
          where: { id: docId }
        });

        if (!document) {
          results.push({
            documentId: docId,
            success: false,
            message: 'Document not found'
          });
          continue;
        }

        let updateData: any = {
          verifiedBy: adminName,
          verifiedAt: new Date(),
          verificationNotes
        };

        switch (action) {
          case 'approve':
            updateData.verificationStatus = 'verified';
            updateData.isVerified = true;
            break;
          case 'reject':
            updateData.verificationStatus = 'rejected';
            updateData.isVerified = false;
            updateData.rejectionReason = rejectionReason;
            break;
          case 'request_resubmission':
            updateData.verificationStatus = 'resubmission_required';
            updateData.isVerified = false;
            updateData.resubmissionReason = rejectionReason;
            break;
        }

        await prisma.documentVerification.update({
          where: { id: docId },
          data: updateData
        });

        // Create audit log
        await prisma.auditLog.create({
          data: {
            tableName: 'document_verification',
            recordId: docId,
            action: action.toUpperCase(),
            oldValues: { 
              verificationStatus: document.verificationStatus 
            },
            newValues: {
              verificationStatus: updateData.verificationStatus,
              verifiedBy: adminName
            },
            userType: 'admin',
            userId: adminId,
            userEmail: adminName,
            timestamp: new Date()
          }
        });

        results.push({
          documentId: docId,
          success: true,
          message: `Document ${action}d successfully`
        });

        // Check registration completion
        if (action === 'approve') {
          await checkRegistrationDocumentCompletion(document.registrationId);
        }

      } catch (error) {
        results.push({
          documentId: docId,
          success: false,
          message: 'Failed to process document'
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    return NextResponse.json({
      success: true,
      data: {
        results,
        summary: {
          total: results.length,
          successful: successCount,
          failed: failureCount
        }
      },
      message: `Bulk verification completed: ${successCount} successful, ${failureCount} failed`
    });

  } catch (error) {
    console.error('Bulk document verification error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to check if all documents for a registration are verified
async function checkRegistrationDocumentCompletion(registrationId: string) {
  try {
    const documents = await prisma.documentVerification.findMany({
      where: { registrationId }
    });

    const allVerified = documents.length > 0 && documents.every(doc => doc.isVerified);
    const hasRejected = documents.some(doc => doc.verificationStatus === 'rejected');
    const hasResubmissionRequired = documents.some(doc => doc.verificationStatus === 'resubmission_required');

    let registrationStatus = 'pending';
    if (allVerified) {
      registrationStatus = 'documents_verified';
    } else if (hasRejected) {
      registrationStatus = 'documents_rejected';
    } else if (hasResubmissionRequired) {
      registrationStatus = 'documents_resubmission_required';
    }

    // Update registration status based on document verification
    // This would need to be implemented based on your registration system
    // For now, we'll create a notification or log entry

    await prisma.auditLog.create({
      data: {
        tableName: 'registrations',
        recordId: registrationId,
        action: 'DOCUMENT_VERIFICATION_COMPLETE',
        oldValues: {},
        newValues: {
          documentVerificationStatus: registrationStatus,
          allDocumentsVerified: allVerified
        },
        userType: 'system',
        userId: 'system',
        userEmail: 'system',
        timestamp: new Date()
      }
    });

  } catch (error) {
    console.error('Error checking registration document completion:', error);
  }
}
