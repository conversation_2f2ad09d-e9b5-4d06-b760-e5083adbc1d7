// Simple seed script for demo purposes
// This creates minimal data needed for testing

const { PrismaClient } = require('./src/generated/prisma');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function hashPassword(password) {
  return await bcrypt.hash(password, 12);
}

async function main() {
  console.log('🌱 Creating minimal demo data...');

  try {
    // Create basic subjects
    console.log('📚 Creating subjects...');
    await prisma.subject.createMany({
      data: [
        { id: 'ENG', code: 'ENG', name: 'English Language', level: 'O Level', isActive: true },
        { id: 'MAT', code: 'MAT', name: 'Mathematics', level: 'O Level', isActive: true },
        { id: 'PHY', code: 'PHY', name: 'Physics', level: 'A Level', isActive: true },
        { id: 'CHE', code: 'CHE', name: 'Chemistry', level: 'A Level', isActive: true }
      ],
      skipDuplicates: true
    });

    // Create exam centers
    console.log('🏫 Creating exam centers...');
    await prisma.examCenter.createMany({
      data: [
        {
          id: 'DEMO-001',
          name: 'Demo Examination Center',
          code: 'DEMO-001',
          location: 'Yaoundé',
          address: 'Demo Address, Yaoundé',
          capacity: 500,
          isActive: true
        }
      ],
      skipDuplicates: true
    });

    // Create exam session
    console.log('📅 Creating exam session...');
    await prisma.examSession.createMany({
      data: [
        {
          id: 'SESSION-2025',
          name: '2025 A Level Session',
          level: 'A Level',
          startDate: new Date('2025-06-01'),
          endDate: new Date('2025-06-30'),
          registrationStart: new Date('2025-01-01'),
          registrationEnd: new Date('2025-05-01'),
          isActive: true
        }
      ],
      skipDuplicates: true
    });

    console.log('✅ Basic demo data created successfully!');
    console.log('');
    console.log('🔐 For user authentication, the system uses:');
    console.log('- Mock authentication for demo purposes');
    console.log('- Check the authentication system in src/lib/auth.ts');
    console.log('');
    console.log('📊 Data created:');
    console.log('- Subjects: 4');
    console.log('- Exam Centers: 1');
    console.log('- Exam Sessions: 1');

  } catch (error) {
    console.error('❌ Error creating demo data:', error);
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
