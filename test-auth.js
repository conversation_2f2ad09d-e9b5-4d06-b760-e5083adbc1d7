// Simple authentication test script
const http = require('http');

function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            data: jsonBody
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAuthentication() {
  console.log('🔐 Testing Authentication System...\n');

  // Test 1: Demo Student Login
  console.log('1. Testing Demo Student Login...');
  try {
    const loginData = {
      email: '<EMAIL>',
      password: 'demo123',
      userType: 'student'
    };

    const result = await makeRequest('POST', '/api/auth/login', loginData);
    console.log(`   Status: ${result.status}`);
    console.log(`   Response:`, result.data);
    
    if (result.status === 200 && result.data.success) {
      console.log('   ✅ Demo student login successful!');
      console.log(`   Token: ${result.data.token ? 'Generated' : 'Not provided'}`);
    } else {
      console.log('   ❌ Demo student login failed');
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
  }

  console.log('\n2. Testing Admin Login...');
  try {
    const adminData = {
      email: '<EMAIL>',
      password: 'admin123',
      userType: 'admin'
    };

    const result = await makeRequest('POST', '/api/auth/login', adminData);
    console.log(`   Status: ${result.status}`);
    console.log(`   Response:`, result.data);
    
    if (result.status === 200 && result.data.success) {
      console.log('   ✅ Admin login successful!');
    } else {
      console.log('   ❌ Admin login failed');
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
  }

  console.log('\n3. Testing Invalid Login...');
  try {
    const invalidData = {
      email: '<EMAIL>',
      password: 'wrongpassword',
      userType: 'student'
    };

    const result = await makeRequest('POST', '/api/auth/login', invalidData);
    console.log(`   Status: ${result.status}`);
    console.log(`   Response:`, result.data);
    
    if (result.status === 401 || result.status === 403) {
      console.log('   ✅ Invalid login properly rejected');
    } else {
      console.log('   ❌ Invalid login should be rejected');
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
  }

  console.log('\n🔐 Authentication testing complete!');
}

testAuthentication().catch(console.error);
