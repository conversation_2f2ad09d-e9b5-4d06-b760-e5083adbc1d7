# Docker Compose for GCE System Production Environment

version: '3.8'

services:
  # Main Application (Multiple instances for load balancing)
  app1:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: gce-system-app1
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - BCRYPT_ROUNDS=12
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL}
      - CERTIFICATE_SIGNING_KEY=${CERTIFICATE_SIGNING_KEY}
    volumes:
      - app_uploads:/app/uploads
      - app_backups:/app/backups
      - app_certificates:/app/certificates
      - app_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - gce-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  app2:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: gce-system-app2
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - BCRYPT_ROUNDS=12
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL}
      - CERTIFICATE_SIGNING_KEY=${CERTIFICATE_SIGNING_KEY}
    volumes:
      - app_uploads:/app/uploads
      - app_backups:/app/backups
      - app_certificates:/app/certificates
      - app_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - gce-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database with replication
  postgres:
    image: postgres:15-alpine
    container_name: gce-system-postgres-primary
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_REPLICATION_USER=${POSTGRES_REPLICATION_USER}
      - POSTGRES_REPLICATION_PASSWORD=${POSTGRES_REPLICATION_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./config/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./scripts/init-db-prod.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - gce-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis Cluster
  redis:
    image: redis:7-alpine
    container_name: gce-system-redis
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./config/redis-prod.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - gce-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  # Nginx Load Balancer with SSL
  nginx:
    image: nginx:alpine
    container_name: gce-system-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx-prod.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - app_uploads:/var/www/uploads:ro
      - app_certificates:/var/www/certificates:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - app1
      - app2
    networks:
      - gce-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: gce-system-prometheus
    volumes:
      - ./config/prometheus-prod.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.external-url=https://${DOMAIN}/prometheus'
    networks:
      - gce-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: gce-system-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=https://${DOMAIN}/grafana
      - GF_SECURITY_SECRET_KEY=${GRAFANA_SECRET_KEY}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - gce-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Automated Backup Service
  backup:
    image: postgres:15-alpine
    container_name: gce-system-backup
    environment:
      - PGPASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - S3_BUCKET=${S3_BACKUP_BUCKET}
    volumes:
      - app_backups:/backups
      - ./scripts/backup-prod.sh:/backup.sh:ro
    command: |
      sh -c "
        echo '0 2 * * * /backup.sh' | crontab -
        echo '0 */6 * * * /backup.sh incremental' | crontab -
        crond -f
      "
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - gce-network
    restart: unless-stopped

  # Log Aggregation
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: gce-system-fluentd
    volumes:
      - ./config/fluentd.conf:/fluentd/etc/fluent.conf:ro
      - app_logs:/var/log/app:ro
      - nginx_logs:/var/log/nginx:ro
    ports:
      - "24224:24224"
    networks:
      - gce-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  app_uploads:
    driver: local
  app_backups:
    driver: local
  app_certificates:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  gce-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
