# 🐘 Database Migration Guide: SQLite to PostgreSQL

## 📋 Overview

This guide walks you through migrating your GCE System from SQLite to PostgreSQL for production deployment. The migration includes data transfer, schema setup, and performance optimization.

## 🎯 Prerequisites

### System Requirements
- **PostgreSQL 14+** installed and running
- **Node.js 18+** with npm/yarn
- **Administrative access** to PostgreSQL server
- **Backup space** for SQLite data

### Environment Setup
```bash
# Check PostgreSQL installation
psql --version

# Check Node.js version
node --version

# Verify npm scripts are available
npm run --help
```

## 🚀 Quick Migration (Automated)

### Step 1: Run Automated Setup
```bash
# Run the automated PostgreSQL setup script
npm run db:setup
```

This script will:
- ✅ Install PostgreSQL (if needed)
- ✅ Create database and user
- ✅ Set up schemas
- ✅ Run migrations
- ✅ Seed initial data

### Step 2: Migrate Existing Data
```bash
# Migrate data from SQLite to PostgreSQL
npm run db:migrate-from-sqlite
```

### Step 3: Verify Migration
```bash
# Run health check
npm run db:health

# Open database studio
npm run db:studio
```

## 🔧 Manual Migration (Step-by-Step)

### Step 1: Install PostgreSQL

#### Windows
```powershell
# Using winget
winget install PostgreSQL.PostgreSQL

# Or download from: https://www.postgresql.org/download/windows/
```

#### macOS
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### Step 2: Create Database and User

```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database
CREATE DATABASE gce_system;

-- Create user
CREATE USER gce_user WITH PASSWORD 'gce_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE gce_system TO gce_user;
ALTER USER gce_user CREATEDB;

-- Connect to new database
\c gce_system

-- Create schemas
CREATE SCHEMA IF NOT EXISTS o_level_students;
CREATE SCHEMA IF NOT EXISTS a_level_students;
CREATE SCHEMA IF NOT EXISTS teacher_auth;
CREATE SCHEMA IF NOT EXISTS examiner_auth;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO gce_user;
GRANT ALL ON SCHEMA o_level_students TO gce_user;
GRANT ALL ON SCHEMA a_level_students TO gce_user;
GRANT ALL ON SCHEMA teacher_auth TO gce_user;
GRANT ALL ON SCHEMA examiner_auth TO gce_user;
```

### Step 3: Update Environment Configuration

```bash
# Update .env file
DATABASE_URL="postgresql://gce_user:gce_password@localhost:5432/gce_system?schema=public"
```

### Step 4: Run Database Migrations

```bash
# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate:deploy

# Seed database
npm run db:seed
```

### Step 5: Migrate Existing Data

```bash
# Create backup of SQLite data
npm run db:backup -- --format=json --output=./sqlite-backup

# Migrate data to PostgreSQL
npm run db:migrate-from-sqlite
```

## 📊 Database Management Commands

### Daily Operations
```bash
# Health check
npm run db:health

# View database
npm run db:studio

# Create backup
npm run db:backup

# Reset database (development only)
npm run db:reset
```

### Backup Operations
```bash
# Full backup (schema + data)
npm run db:backup

# Schema only backup
npm run db:backup -- --schema-only

# Compressed backup
npm run db:backup -- --compress

# Custom output directory
npm run db:backup -- --output=./my-backups
```

### Restore Operations
```bash
# Restore from backup
npm run db:restore ./backups/backup-2025-01-08T10-30-00-000Z

# Restore with existing data removal
npm run db:restore ./backups/latest -- --drop-existing

# Restore schema only
npm run db:restore ./backups/schema-backup -- --schema-only
```

## 🔒 Security Configuration

### Database Security
```sql
-- Enable row-level security (RLS)
ALTER TABLE o_level_students.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE a_level_students.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_auth.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE examiner_auth.users ENABLE ROW LEVEL SECURITY;

-- Create security policies (example)
CREATE POLICY user_isolation ON o_level_students.users
    USING (id = current_setting('app.current_user_id'));
```

### Connection Security
```bash
# Update .env for production
DATABASE_URL="postgresql://gce_user:secure_password@localhost:5432/gce_system?sslmode=require"

# Additional security settings
BCRYPT_ROUNDS=12
JWT_SECRET=your_secure_jwt_secret_here
```

## 🚨 Troubleshooting

### Common Issues

#### Connection Refused
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Start PostgreSQL
sudo systemctl start postgresql

# Check port availability
netstat -an | grep 5432
```

#### Permission Denied
```sql
-- Grant additional privileges
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO gce_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO gce_user;
```

#### Migration Errors
```bash
# Reset migrations (development only)
npm run db:reset

# Check migration status
npx prisma migrate status

# Force migration
npx prisma migrate deploy --force
```

#### Schema Issues
```bash
# Regenerate Prisma client
npm run db:generate

# Validate schema
npx prisma validate
```

### Performance Issues

#### Slow Queries
```sql
-- Enable query logging
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Reload configuration
SELECT pg_reload_conf();
```

#### Index Optimization
```sql
-- Create indexes for common queries
CREATE INDEX idx_students_email ON o_level_students.users(email);
CREATE INDEX idx_students_center ON o_level_students.users(center_code);
CREATE INDEX idx_audit_timestamp ON public.audit_logs(timestamp);
```

## 📈 Performance Optimization

### Database Configuration
```sql
-- Optimize PostgreSQL settings
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
```

### Connection Pooling
```bash
# Install pg_bouncer for connection pooling
sudo apt install pgbouncer

# Configure connection string
DATABASE_URL="postgresql://gce_user:password@localhost:6432/gce_system"
```

## 🔄 Maintenance Schedule

### Daily
- ✅ Health check monitoring
- ✅ Backup verification
- ✅ Performance metrics review

### Weekly
- ✅ Full database backup
- ✅ Index maintenance
- ✅ Log rotation

### Monthly
- ✅ Security audit
- ✅ Performance optimization
- ✅ Capacity planning review

## 📞 Support

### Getting Help
1. **Check logs**: `tail -f /var/log/postgresql/postgresql-*.log`
2. **Run health check**: `npm run db:health`
3. **Review documentation**: This guide and PostgreSQL docs
4. **Community support**: PostgreSQL community forums

### Emergency Procedures
1. **Database corruption**: Restore from latest backup
2. **Performance issues**: Check slow query log
3. **Connection issues**: Restart PostgreSQL service
4. **Data loss**: Use point-in-time recovery

---

## ✅ Migration Checklist

- [ ] PostgreSQL installed and running
- [ ] Database and user created
- [ ] Environment variables updated
- [ ] Prisma client generated
- [ ] Migrations executed
- [ ] Data migrated from SQLite
- [ ] Health check passed
- [ ] Backup system configured
- [ ] Security policies implemented
- [ ] Performance monitoring setup
- [ ] Documentation updated
- [ ] Team trained on new procedures

**🎉 Congratulations! Your GCE System is now running on PostgreSQL!**
