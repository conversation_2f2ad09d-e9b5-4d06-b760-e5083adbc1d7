// Test login after CSRF fix
const http = require('http');

function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            data: jsonBody
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testLoginFixed() {
  console.log('🔧 Testing Login After CSRF Fix...\n');

  // Wait a moment for server to be ready
  console.log('⏳ Waiting for server to be ready...');
  await new Promise(resolve => setTimeout(resolve, 3000));

  // Test server health first
  console.log('🏥 Testing server health...');
  try {
    const healthResult = await makeRequest('GET', '/');
    console.log(`   Server Status: ${healthResult.status}`);
    
    if (healthResult.status !== 200) {
      console.log('   ❌ Server not ready yet. Please wait and try again.');
      return;
    }
    console.log('   ✅ Server is ready!');
  } catch (error) {
    console.log('   ❌ Server not responding:', error.message);
    return;
  }

  console.log('\n🎓 Testing Student Login...');
  try {
    const loginData = {
      email: '<EMAIL>',
      password: 'demo123',
      userType: 'student'
    };

    const result = await makeRequest('POST', '/api/auth/login', loginData);
    console.log(`   Status: ${result.status}`);
    
    if (result.status === 200 && result.data.success) {
      console.log('   ✅ LOGIN SUCCESSFUL!');
      console.log(`   Student: ${result.data.user?.fullName || 'Demo Student'}`);
      console.log(`   Token: ${result.data.token ? 'Generated ✅' : 'Not provided'}`);
      console.log(`   User Type: ${result.data.userType || 'student'}`);
      console.log('\n🎉 DEMO IS READY!');
      console.log('   You can now login in the browser with:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: demo123');
      console.log('   User Type: Student');
    } else if (result.status === 429) {
      console.log('   ⏰ Rate limiting still active. Wait 15 minutes or restart server.');
    } else if (result.status === 403) {
      console.log('   🛡️ CSRF protection still active. Check middleware configuration.');
    } else {
      console.log('   ❌ Login failed');
      console.log(`   Response:`, result.data);
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
  }

  console.log('\n📋 Next Steps:');
  console.log('   1. Open browser: http://localhost:3000/auth/Login');
  console.log('   2. <NAME_EMAIL> / demo123');
  console.log('   3. Explore the student dashboard');
  console.log('   4. Show bilingual support and responsive design');
  console.log('\n🎭 Demo talking points:');
  console.log('   - Enterprise-grade security (temporarily configured for demo)');
  console.log('   - Real-time database integration');
  console.log('   - Modern React 18 + Next.js architecture');
  console.log('   - Comprehensive student portal features');
}

testLoginFixed().catch(console.error);
