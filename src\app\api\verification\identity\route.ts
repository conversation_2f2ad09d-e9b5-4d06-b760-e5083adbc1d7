import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/postgresDb';

// POST /api/verification/identity - Verify identity documents
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      registrationId,
      nationalId,
      documentType, // 'national_id', 'passport', 'birth_certificate'
      documentNumber,
      documentImage, // Base64 encoded image
      faceImage, // Base64 encoded face photo
      verificationMethod // 'manual', 'automated', 'hybrid'
    } = body;

    if (!registrationId || !nationalId || !documentType || !documentNumber) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create identity verification record
    const verification = await prisma.identityVerification.create({
      data: {
        registrationId,
        nationalId,
        documentType,
        documentNumber,
        documentImage,
        faceImage,
        verificationMethod: verificationMethod || 'manual',
        status: 'pending',
        submittedAt: new Date()
      }
    });

    // Perform automated checks if available
    let automatedResult = null;
    if (verificationMethod === 'automated' || verificationMethod === 'hybrid') {
      automatedResult = await performAutomatedVerification(verification);
    }

    // If automated verification is successful, update status
    if (automatedResult?.success) {
      await prisma.identityVerification.update({
        where: { id: verification.id },
        data: {
          status: 'verified',
          verificationScore: automatedResult.score,
          automatedChecks: automatedResult.checks,
          verifiedAt: new Date(),
          verifiedBy: 'automated_system'
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        verificationId: verification.id,
        status: automatedResult?.success ? 'verified' : 'pending',
        automatedResult
      },
      message: 'Identity verification submitted successfully'
    });

  } catch (error) {
    console.error('Identity verification error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/verification/identity - Get identity verification status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const registrationId = searchParams.get('registrationId');
    const verificationId = searchParams.get('verificationId');

    if (!registrationId && !verificationId) {
      return NextResponse.json(
        { success: false, message: 'Registration ID or Verification ID required' },
        { status: 400 }
      );
    }

    let verification;
    if (verificationId) {
      verification = await prisma.identityVerification.findUnique({
        where: { id: verificationId }
      });
    } else {
      verification = await prisma.identityVerification.findFirst({
        where: { registrationId },
        orderBy: { submittedAt: 'desc' }
      });
    }

    if (!verification) {
      return NextResponse.json(
        { success: false, message: 'Identity verification not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: verification,
      message: 'Identity verification retrieved successfully'
    });

  } catch (error) {
    console.error('Get identity verification error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/verification/identity - Update verification status (admin only)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      verificationId,
      status, // 'verified', 'rejected', 'requires_manual_review'
      adminId,
      adminName,
      verificationNotes,
      rejectionReason
    } = body;

    if (!verificationId || !status || !adminId) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    const verification = await prisma.identityVerification.findUnique({
      where: { id: verificationId }
    });

    if (!verification) {
      return NextResponse.json(
        { success: false, message: 'Identity verification not found' },
        { status: 404 }
      );
    }

    const updateData: any = {
      status,
      verifiedBy: adminName,
      verifiedAt: new Date(),
      verificationNotes
    };

    if (status === 'rejected') {
      updateData.rejectionReason = rejectionReason;
    }

    const updatedVerification = await prisma.identityVerification.update({
      where: { id: verificationId },
      data: updateData
    });

    // Create audit log
    await prisma.auditLog.create({
      data: {
        tableName: 'identity_verification',
        recordId: verificationId,
        action: `IDENTITY_${status.toUpperCase()}`,
        oldValues: { status: verification.status },
        newValues: { 
          status,
          verifiedBy: adminName
        },
        userType: 'admin',
        userId: adminId,
        userEmail: adminName,
        timestamp: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedVerification,
      message: `Identity verification ${status} successfully`
    });

  } catch (error) {
    console.error('Update identity verification error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Automated verification function (mock implementation)
async function performAutomatedVerification(verification: any) {
  try {
    // Mock automated verification - in production, integrate with:
    // - Government ID verification services
    // - OCR services for document text extraction
    // - Face matching services
    // - Document authenticity checks

    const checks = {
      documentFormat: true, // Check if document format is valid
      documentAuthenticity: Math.random() > 0.1, // 90% pass rate
      faceMatch: Math.random() > 0.2, // 80% pass rate
      nationalIdValidation: await validateNationalId(verification.nationalId),
      documentNumberValidation: true
    };

    const score = Object.values(checks).filter(Boolean).length / Object.keys(checks).length * 100;
    const success = score >= 80; // 80% threshold for automated approval

    return {
      success,
      score,
      checks,
      confidence: score,
      timestamp: new Date()
    };

  } catch (error) {
    console.error('Automated verification error:', error);
    return {
      success: false,
      score: 0,
      checks: {},
      error: 'Automated verification failed'
    };
  }
}

// Mock national ID validation
async function validateNationalId(nationalId: string): Promise<boolean> {
  try {
    // Mock validation - in production, integrate with government database
    // Check format, checksum, etc.
    
    // Basic format validation for Cameroon National ID
    const cameroonIdPattern = /^\d{9}$/; // 9 digits
    if (!cameroonIdPattern.test(nationalId)) {
      return false;
    }

    // Mock database check (90% pass rate)
    return Math.random() > 0.1;

  } catch (error) {
    console.error('National ID validation error:', error);
    return false;
  }
}
