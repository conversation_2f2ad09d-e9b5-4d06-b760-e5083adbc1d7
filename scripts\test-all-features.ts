#!/usr/bin/env tsx
/**
 * Comprehensive Feature Testing Script
 * Tests all implemented features systematically
 */

import { AuthService } from '../src/lib/auth';
import { RateLimiter, InputValidator, CSRFProtection } from '../src/lib/security';
import { ResultProcessor } from '../src/lib/resultProcessing';
import { CertificateGenerator } from '../src/lib/certificateGenerator';
import { NotificationService } from '../src/lib/notificationService';
import { CacheManager, PerformanceMonitor } from '../src/lib/performance';
import { MetricsCollector, ErrorTracker, UserAnalytics } from '../src/lib/monitoring';

interface TestResult {
  feature: string;
  test: string;
  passed: boolean;
  details: string;
  duration: number;
}

class FeatureTester {
  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Comprehensive Feature Testing...\n');

    await this.testAuthenticationSystem();
    await this.testSecurityFeatures();
    await this.testResultProcessing();
    await this.testCertificateGeneration();
    await this.testNotificationSystem();
    await this.testPerformanceOptimization();
    await this.testMonitoringSystem();

    this.generateTestReport();
  }

  private async runTest(feature: string, testName: string, testFn: () => Promise<boolean>): Promise<void> {
    const startTime = Date.now();
    try {
      const passed = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        feature,
        test: testName,
        passed,
        details: passed ? 'Test passed successfully' : 'Test failed',
        duration
      });

      const status = passed ? '✅' : '❌';
      console.log(`${status} ${feature} - ${testName} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        feature,
        test: testName,
        passed: false,
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration
      });
      console.log(`❌ ${feature} - ${testName} (${duration}ms) - Error: ${error}`);
    }
  }

  // 1. Authentication System Tests
  private async testAuthenticationSystem(): Promise<void> {
    console.log('\n🔐 Testing Authentication System...');

    await this.runTest('Authentication', 'Token Generation', async () => {
      const user = {
        id: 'test-user-123',
        email: '<EMAIL>',
        userType: 'o_level_student' as const,
        fullName: 'Test Student'
      };

      const tokens = AuthService.generateTokens(user);
      return !!(tokens.accessToken && tokens.refreshToken && tokens.expiresIn);
    });

    await this.runTest('Authentication', 'Token Verification', async () => {
      const user = {
        id: 'test-user-123',
        email: '<EMAIL>',
        userType: 'o_level_student' as const,
        fullName: 'Test Student'
      };

      const tokens = AuthService.generateTokens(user);
      const verification = AuthService.verifyToken(tokens.accessToken);
      return verification.valid;
    });

    await this.runTest('Authentication', 'Invalid Token Rejection', async () => {
      const verification = AuthService.verifyToken('invalid-token');
      return !verification.valid;
    });
  }

  // 2. Security Features Tests
  private async testSecurityFeatures(): Promise<void> {
    console.log('\n🛡️ Testing Security Features...');

    await this.runTest('Security', 'Rate Limiting', async () => {
      const result1 = await RateLimiter.checkRateLimit('test-ip-1', '/api/test', {
        windowMs: 60000,
        maxRequests: 5
      });
      
      const result2 = await RateLimiter.checkRateLimit('test-ip-1', '/api/test', {
        windowMs: 60000,
        maxRequests: 5
      });

      return result1.allowed && result2.allowed && result1.remaining > result2.remaining;
    });

    await this.runTest('Security', 'Email Validation', async () => {
      const validEmail = InputValidator.validateEmail('<EMAIL>');
      const invalidEmail = InputValidator.validateEmail('invalid-email');
      
      return validEmail.valid && !invalidEmail.valid;
    });

    await this.runTest('Security', 'Password Validation', async () => {
      const strongPassword = InputValidator.validatePassword('StrongP@ss123');
      const weakPassword = InputValidator.validatePassword('weak');
      
      return strongPassword.valid && !weakPassword.valid;
    });

    await this.runTest('Security', 'XSS Protection', async () => {
      const maliciousInput = '<script>alert("xss")</script>Hello';
      const sanitized = InputValidator.sanitizeString(maliciousInput);
      
      return !sanitized.includes('<script>') && sanitized.includes('Hello');
    });

    await this.runTest('Security', 'CSRF Token Generation', async () => {
      const sessionId = 'test-session-123';
      const token = CSRFProtection.generateToken(sessionId);
      const isValid = CSRFProtection.validateToken(sessionId, token);
      
      return typeof token === 'string' && token.length > 0 && isValid;
    });
  }

  // 3. Result Processing Tests
  private async testResultProcessing(): Promise<void> {
    console.log('\n📊 Testing Result Processing...');

    await this.runTest('Result Processing', 'Raw Results Validation', async () => {
      const validResults = [
        {
          studentId: 'student-1',
          candidateNumber: 'TEST001',
          subjects: [
            { code: 'ENG', rawScore: 85 },
            { code: 'MAT', rawScore: 78 }
          ]
        }
      ];

      const validation = ResultProcessor.validateRawResults(validResults);
      return validation.valid;
    });

    await this.runTest('Result Processing', 'Invalid Results Rejection', async () => {
      const invalidResults = [
        {
          studentId: '',
          candidateNumber: 'TEST001',
          subjects: []
        }
      ];

      const validation = ResultProcessor.validateRawResults(invalidResults);
      return !validation.valid && validation.errors.length > 0;
    });

    await this.runTest('Result Processing', 'Student Result Processing', async () => {
      const result = await ResultProcessor.processStudentResult(
        'student-1',
        'TEST001',
        'O Level',
        'June 2025',
        [
          { code: 'ENG', rawScore: 85 },
          { code: 'MAT', rawScore: 78 },
          { code: 'PHY', rawScore: 72 }
        ]
      );

      return !!(result.studentId && result.subjects.length === 3 && result.overallGrade);
    });
  }

  // 4. Certificate Generation Tests
  private async testCertificateGeneration(): Promise<void> {
    console.log('\n📜 Testing Certificate Generation...');

    await this.runTest('Certificate Generation', 'Certificate Data Validation', async () => {
      const validData = {
        studentId: 'student-1',
        candidateNumber: 'TEST001',
        fullName: 'Test Student',
        examLevel: 'O Level' as const,
        examSession: 'June 2025',
        examYear: '2025',
        centerName: 'Test Center',
        centerCode: 'TC001',
        subjects: [
          { code: 'ENG', name: 'English Language', grade: 'A', points: 1 },
          { code: 'MAT', name: 'Mathematics', grade: 'B', points: 2 }
        ],
        overallGrade: 'Credit',
        classification: 'Credit',
        issueDate: new Date(),
        qualifications: ['O Level Certificate with Credit']
      };

      const result = await CertificateGenerator.generateCertificate(validData);
      return result.success && !!result.certificate;
    });

    await this.runTest('Certificate Generation', 'Certificate Verification', async () => {
      const validData = {
        studentId: 'student-2',
        candidateNumber: 'TEST002',
        fullName: 'Test Student 2',
        examLevel: 'O Level' as const,
        examSession: 'June 2025',
        examYear: '2025',
        centerName: 'Test Center',
        centerCode: 'TC001',
        subjects: [
          { code: 'ENG', name: 'English Language', grade: 'A', points: 1 }
        ],
        overallGrade: 'Distinction',
        classification: 'Distinction',
        issueDate: new Date(),
        qualifications: ['O Level Certificate with Distinction']
      };

      const result = await CertificateGenerator.generateCertificate(validData);
      if (!result.success || !result.certificate) return false;

      const verification = CertificateGenerator.verifyCertificate(result.certificate.verificationCode);
      return verification.valid && verification.certificate?.id === result.certificate.id;
    });
  }

  // 5. Notification System Tests
  private async testNotificationSystem(): Promise<void> {
    console.log('\n📧 Testing Notification System...');

    await this.runTest('Notification System', 'Template Rendering', async () => {
      const templates = NotificationService.getTemplates('result');
      const template = templates.find(t => t.id === 'result_published');
      
      if (!template) return false;

      const rendered = NotificationService.renderTemplate(template, {
        studentName: 'Test Student',
        examLevel: 'O Level',
        examSession: 'June 2025',
        overallGrade: 'Credit',
        status: 'pass',
        portalUrl: 'https://gce.cm/student/results'
      });

      return !!(rendered.subject && rendered.content && 
               rendered.content.includes('Test Student') && 
               rendered.content.includes('Credit'));
    });

    await this.runTest('Notification System', 'Notification Sending', async () => {
      const recipients = [
        {
          id: 'student-1',
          name: 'Test Student',
          email: '<EMAIL>',
          userType: 'student' as const,
          preferences: { email: true, sms: false, push: true }
        }
      ];

      const result = await NotificationService.sendNotification({
        templateId: 'result_published',
        recipients,
        variables: {
          studentName: 'Test Student',
          examLevel: 'O Level',
          examSession: 'June 2025',
          overallGrade: 'Credit',
          status: 'pass',
          portalUrl: 'https://gce.cm/student/results'
        },
        priority: 'high'
      });

      return result.success && !!result.notificationId;
    });
  }

  // 6. Performance Optimization Tests
  private async testPerformanceOptimization(): Promise<void> {
    console.log('\n⚡ Testing Performance Optimization...');

    await this.runTest('Performance', 'Cache Operations', async () => {
      const testKey = 'test-cache-key';
      const testData = { message: 'Hello Cache', timestamp: Date.now() };

      // Set cache
      CacheManager.set(testKey, testData, 5000);

      // Get cache
      const cached = CacheManager.get(testKey);

      // Verify data
      return !!(cached && cached.message === testData.message);
    });

    await this.runTest('Performance', 'Cache Statistics', async () => {
      const stats = CacheManager.getStats();
      return typeof stats.size === 'number' && Array.isArray(stats.entries);
    });

    await this.runTest('Performance', 'Performance Monitoring', async () => {
      PerformanceMonitor.record('test.metric', 150, 'ms', {
        operation: 'test',
        success: true
      });

      const stats = PerformanceMonitor.getStats('test.metric');
      return stats.count > 0 && stats.average > 0;
    });
  }

  // 7. Monitoring System Tests
  private async testMonitoringSystem(): Promise<void> {
    console.log('\n📊 Testing Monitoring System...');

    await this.runTest('Monitoring', 'Metrics Collection', async () => {
      MetricsCollector.record('test.api.requests', 1, 'count', { endpoint: '/api/test' });
      MetricsCollector.record('test.api.response_time', 250, 'ms', { endpoint: '/api/test' });

      const requestStats = MetricsCollector.getStats('test.api.requests');
      const responseStats = MetricsCollector.getStats('test.api.response_time');

      return requestStats.count > 0 && responseStats.average > 0;
    });

    await this.runTest('Monitoring', 'Error Tracking', async () => {
      ErrorTracker.track('Test error message', 'medium', {
        component: 'test',
        operation: 'feature-test'
      });

      const errors = ErrorTracker.getErrors('medium');
      return errors.length > 0 && errors[0].message === 'Test error message';
    });

    await this.runTest('Monitoring', 'User Analytics', async () => {
      UserAnalytics.track('test_event', {
        feature: 'testing',
        action: 'feature_test'
      }, 'test-user-123');

      const analytics = UserAnalytics.getAnalytics();
      return analytics.totalEvents > 0 && analytics.topEvents.length > 0;
    });
  }

  private generateTestReport(): void {
    console.log('\n📋 Test Results Summary');
    console.log('========================\n');

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const passRate = ((passedTests / totalTests) * 100).toFixed(1);

    // Group by feature
    const byFeature = this.results.reduce((acc, result) => {
      if (!acc[result.feature]) {
        acc[result.feature] = { passed: 0, failed: 0, tests: [] };
      }
      if (result.passed) {
        acc[result.feature].passed++;
      } else {
        acc[result.feature].failed++;
      }
      acc[result.feature].tests.push(result);
      return acc;
    }, {} as Record<string, { passed: number; failed: number; tests: TestResult[] }>);

    // Feature summary
    Object.entries(byFeature).forEach(([feature, stats]) => {
      const featurePassRate = ((stats.passed / (stats.passed + stats.failed)) * 100).toFixed(1);
      console.log(`📋 ${feature}`);
      console.log(`   ✅ Passed: ${stats.passed}`);
      console.log(`   ❌ Failed: ${stats.failed}`);
      console.log(`   📊 Pass Rate: ${featurePassRate}%\n`);

      // Show failed tests
      const failedTests = stats.tests.filter(t => !t.passed);
      if (failedTests.length > 0) {
        console.log(`   ⚠️  Failed Tests:`);
        failedTests.forEach(test => {
          console.log(`      ❌ ${test.test}: ${test.details}`);
        });
        console.log('');
      }
    });

    // Overall summary
    console.log(`📊 Overall Results:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests}`);
    console.log(`   Failed: ${failedTests}`);
    console.log(`   Pass Rate: ${passRate}%`);

    // Performance summary
    const avgDuration = this.results.reduce((sum, r) => sum + r.duration, 0) / totalTests;
    console.log(`   Average Test Duration: ${avgDuration.toFixed(1)}ms`);

    // Recommendations
    console.log('\n💡 Recommendations:');
    if (passRate === '100.0') {
      console.log('   ✅ All tests passed! System is ready for production.');
    } else if (parseFloat(passRate) >= 90) {
      console.log('   ⚠️  Most tests passed. Review failed tests before production.');
    } else {
      console.log('   🚨 Multiple test failures. Address issues before deployment.');
    }

    console.log(`\n🎉 Feature testing completed!`);
  }
}

async function main() {
  const tester = new FeatureTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}

export { FeatureTester };
