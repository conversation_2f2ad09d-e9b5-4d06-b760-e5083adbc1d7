#!/usr/bin/env tsx
/**
 * SQLite to PostgreSQL Migration Script
 * This script migrates data from SQLite to PostgreSQL for the GCE System
 */

import { PrismaClient as SQLitePrismaClient } from '@prisma/client';
import { PrismaClient as PostgreSQLPrismaClient } from '../src/generated/prisma';
import * as fs from 'fs';
import * as path from 'path';

// SQLite client (source)
const sqliteClient = new SQLitePrismaClient({
  datasources: {
    db: {
      url: 'file:./dev.db'
    }
  }
});

// PostgreSQL client (destination)
const postgresClient = new PostgreSQLPrismaClient();

interface MigrationStats {
  tables: string[];
  recordsCopied: { [table: string]: number };
  errors: { [table: string]: string[] };
  startTime: Date;
  endTime?: Date;
}

const migrationStats: MigrationStats = {
  tables: [],
  recordsCopied: {},
  errors: {},
  startTime: new Date()
};

async function checkConnections() {
  console.log('🔍 Checking database connections...');
  
  try {
    await sqliteClient.$connect();
    console.log('✅ SQLite connection successful');
  } catch (error) {
    console.error('❌ SQLite connection failed:', error);
    throw error;
  }

  try {
    await postgresClient.$connect();
    console.log('✅ PostgreSQL connection successful');
  } catch (error) {
    console.error('❌ PostgreSQL connection failed:', error);
    throw error;
  }
}

async function backupSQLiteData() {
  console.log('💾 Creating SQLite backup...');
  
  const backupDir = './backups';
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = path.join(backupDir, `sqlite-backup-${timestamp}.db`);
  
  try {
    fs.copyFileSync('./dev.db', backupPath);
    console.log(`✅ SQLite backup created: ${backupPath}`);
    return backupPath;
  } catch (error) {
    console.error('❌ Failed to create SQLite backup:', error);
    throw error;
  }
}

async function migrateTable<T>(
  tableName: string,
  sqliteQuery: () => Promise<T[]>,
  postgresInsert: (data: T[]) => Promise<any>
) {
  console.log(`📊 Migrating ${tableName}...`);
  migrationStats.tables.push(tableName);
  migrationStats.recordsCopied[tableName] = 0;
  migrationStats.errors[tableName] = [];

  try {
    // Fetch data from SQLite
    const data = await sqliteQuery();
    console.log(`   Found ${data.length} records in ${tableName}`);

    if (data.length === 0) {
      console.log(`   ⚠️  No data to migrate for ${tableName}`);
      return;
    }

    // Insert data into PostgreSQL in batches
    const batchSize = 100;
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      try {
        await postgresInsert(batch);
        migrationStats.recordsCopied[tableName] += batch.length;
        console.log(`   ✅ Migrated ${migrationStats.recordsCopied[tableName]}/${data.length} records`);
      } catch (error) {
        const errorMsg = `Batch ${i}-${i + batch.length}: ${error}`;
        migrationStats.errors[tableName].push(errorMsg);
        console.error(`   ❌ ${errorMsg}`);
      }
    }

    console.log(`✅ ${tableName} migration completed: ${migrationStats.recordsCopied[tableName]} records`);
  } catch (error) {
    const errorMsg = `Failed to migrate ${tableName}: ${error}`;
    migrationStats.errors[tableName].push(errorMsg);
    console.error(`❌ ${errorMsg}`);
  }
}

async function migrateAllData() {
  console.log('🚀 Starting data migration...');

  // Check if SQLite database exists
  if (!fs.existsSync('./dev.db')) {
    console.log('⚠️  SQLite database not found. Skipping data migration.');
    return;
  }

  // Migrate shared data first (public schema)
  await migrateTable(
    'subjects',
    () => sqliteClient.subject.findMany(),
    (data) => postgresClient.subject.createMany({ 
      data: data.map(item => ({
        ...item,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt)
      })),
      skipDuplicates: true 
    })
  );

  await migrateTable(
    'schools',
    () => sqliteClient.school.findMany(),
    (data) => postgresClient.school.createMany({ 
      data: data.map(item => ({
        ...item,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt)
      })),
      skipDuplicates: true 
    })
  );

  await migrateTable(
    'examCenters',
    () => sqliteClient.examCenter.findMany(),
    (data) => postgresClient.examCenter.createMany({ 
      data: data.map(item => ({
        ...item,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt)
      })),
      skipDuplicates: true 
    })
  );

  await migrateTable(
    'examSessions',
    () => sqliteClient.examSession.findMany(),
    (data) => postgresClient.examSession.createMany({ 
      data: data.map(item => ({
        ...item,
        startDate: new Date(item.startDate),
        endDate: new Date(item.endDate),
        registrationStart: new Date(item.registrationStart),
        registrationEnd: new Date(item.registrationEnd),
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt)
      })),
      skipDuplicates: true 
    })
  );

  // Note: User data migration would need to be handled carefully
  // due to the separate schema structure in PostgreSQL
  console.log('⚠️  User data migration requires manual handling due to schema separation');
}

async function generateMigrationReport() {
  migrationStats.endTime = new Date();
  const duration = migrationStats.endTime.getTime() - migrationStats.startTime.getTime();
  
  const report = {
    migration: {
      startTime: migrationStats.startTime.toISOString(),
      endTime: migrationStats.endTime.toISOString(),
      duration: `${Math.round(duration / 1000)}s`,
      tablesProcessed: migrationStats.tables.length
    },
    results: migrationStats.recordsCopied,
    errors: migrationStats.errors,
    summary: {
      totalRecords: Object.values(migrationStats.recordsCopied).reduce((a, b) => a + b, 0),
      tablesWithErrors: Object.keys(migrationStats.errors).filter(table => 
        migrationStats.errors[table].length > 0
      ).length
    }
  };

  const reportPath = `./migration-report-${new Date().toISOString().split('T')[0]}.json`;
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log('\n📊 Migration Report:');
  console.log(`   Duration: ${report.migration.duration}`);
  console.log(`   Tables processed: ${report.migration.tablesProcessed}`);
  console.log(`   Total records migrated: ${report.summary.totalRecords}`);
  console.log(`   Tables with errors: ${report.summary.tablesWithErrors}`);
  console.log(`   Report saved to: ${reportPath}`);
  
  return report;
}

async function main() {
  try {
    console.log('🐘 SQLite to PostgreSQL Migration Tool');
    console.log('=====================================\n');

    await checkConnections();
    await backupSQLiteData();
    await migrateAllData();
    await generateMigrationReport();

    console.log('\n✅ Migration completed successfully!');
    console.log('🔧 Next steps:');
    console.log('   1. Update your .env file to use PostgreSQL');
    console.log('   2. Run: npx prisma migrate deploy');
    console.log('   3. Run: npx prisma generate');
    console.log('   4. Test your application');

  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await sqliteClient.$disconnect();
    await postgresClient.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export { main as migrateSQLiteToPostgreSQL };
