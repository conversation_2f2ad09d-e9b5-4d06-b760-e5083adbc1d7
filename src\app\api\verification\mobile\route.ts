import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/postgresDb';

// POST /api/verification/mobile - Send SMS verification code
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      phoneNumber,
      registrationId,
      verificationType = 'registration' // 'registration', 'login', 'password_reset'
    } = body;

    if (!phoneNumber || !registrationId) {
      return NextResponse.json(
        { success: false, message: 'Phone number and registration ID are required' },
        { status: 400 }
      );
    }

    // Validate phone number format (Cameroon)
    const phoneRegex = /^(\+237|237)?[67]\d{8}$/;
    if (!phoneRegex.test(phoneNumber.replace(/\s/g, ''))) {
      return NextResponse.json(
        { success: false, message: 'Invalid Cameroon phone number format' },
        { status: 400 }
      );
    }

    // Check for recent verification attempts (rate limiting)
    const recentAttempts = await prisma.mobileVerification.count({
      where: {
        phoneNumber,
        createdAt: {
          gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
        }
      }
    });

    if (recentAttempts >= 3) {
      return NextResponse.json(
        { success: false, message: 'Too many verification attempts. Please wait 5 minutes.' },
        { status: 429 }
      );
    }

    // Generate verification code
    const verificationCode = generateVerificationCode();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Save verification record
    const verification = await prisma.mobileVerification.create({
      data: {
        registrationId,
        phoneNumber: normalizePhoneNumber(phoneNumber),
        verificationCode,
        verificationType,
        expiresAt,
        status: 'pending'
      }
    });

    // Send SMS
    const smsResult = await sendSMS(phoneNumber, verificationCode, verificationType);

    if (!smsResult.success) {
      // Update verification status
      await prisma.mobileVerification.update({
        where: { id: verification.id },
        data: { 
          status: 'failed',
          failureReason: smsResult.error
        }
      });

      return NextResponse.json(
        { success: false, message: 'Failed to send SMS verification code' },
        { status: 500 }
      );
    }

    // Update verification status
    await prisma.mobileVerification.update({
      where: { id: verification.id },
      data: { 
        status: 'sent',
        sentAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        verificationId: verification.id,
        phoneNumber: maskPhoneNumber(phoneNumber),
        expiresAt,
        message: 'Verification code sent successfully'
      }
    });

  } catch (error) {
    console.error('Mobile verification error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/verification/mobile - Verify SMS code
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      verificationId,
      verificationCode,
      registrationId
    } = body;

    if (!verificationCode || (!verificationId && !registrationId)) {
      return NextResponse.json(
        { success: false, message: 'Verification code and ID are required' },
        { status: 400 }
      );
    }

    // Find verification record
    let verification;
    if (verificationId) {
      verification = await prisma.mobileVerification.findUnique({
        where: { id: verificationId }
      });
    } else {
      verification = await prisma.mobileVerification.findFirst({
        where: { 
          registrationId,
          status: 'sent'
        },
        orderBy: { createdAt: 'desc' }
      });
    }

    if (!verification) {
      return NextResponse.json(
        { success: false, message: 'Verification record not found' },
        { status: 404 }
      );
    }

    // Check if verification has expired
    if (new Date() > verification.expiresAt) {
      await prisma.mobileVerification.update({
        where: { id: verification.id },
        data: { status: 'expired' }
      });

      return NextResponse.json(
        { success: false, message: 'Verification code has expired' },
        { status: 400 }
      );
    }

    // Check if verification code matches
    if (verification.verificationCode !== verificationCode) {
      // Increment failed attempts
      await prisma.mobileVerification.update({
        where: { id: verification.id },
        data: { 
          failedAttempts: (verification.failedAttempts || 0) + 1
        }
      });

      return NextResponse.json(
        { success: false, message: 'Invalid verification code' },
        { status: 400 }
      );
    }

    // Mark as verified
    await prisma.mobileVerification.update({
      where: { id: verification.id },
      data: {
        status: 'verified',
        verifiedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        verificationId: verification.id,
        phoneNumber: verification.phoneNumber,
        verifiedAt: new Date()
      },
      message: 'Phone number verified successfully'
    });

  } catch (error) {
    console.error('Mobile verification error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/verification/mobile - Get verification status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const verificationId = searchParams.get('verificationId');
    const registrationId = searchParams.get('registrationId');

    if (!verificationId && !registrationId) {
      return NextResponse.json(
        { success: false, message: 'Verification ID or Registration ID required' },
        { status: 400 }
      );
    }

    let verification;
    if (verificationId) {
      verification = await prisma.mobileVerification.findUnique({
        where: { id: verificationId }
      });
    } else {
      verification = await prisma.mobileVerification.findFirst({
        where: { registrationId },
        orderBy: { createdAt: 'desc' }
      });
    }

    if (!verification) {
      return NextResponse.json(
        { success: false, message: 'Verification not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: verification.id,
        phoneNumber: maskPhoneNumber(verification.phoneNumber),
        status: verification.status,
        verificationType: verification.verificationType,
        expiresAt: verification.expiresAt,
        verifiedAt: verification.verifiedAt,
        failedAttempts: verification.failedAttempts || 0
      }
    });

  } catch (error) {
    console.error('Get mobile verification error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper functions
function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

function normalizePhoneNumber(phoneNumber: string): string {
  // Normalize to +237XXXXXXXXX format
  const cleaned = phoneNumber.replace(/\s/g, '');
  if (cleaned.startsWith('+237')) {
    return cleaned;
  } else if (cleaned.startsWith('237')) {
    return '+' + cleaned;
  } else {
    return '+237' + cleaned;
  }
}

function maskPhoneNumber(phoneNumber: string): string {
  // Mask middle digits: +237XXXX1234 -> +237****1234
  if (phoneNumber.length >= 8) {
    const start = phoneNumber.substring(0, 4);
    const end = phoneNumber.substring(phoneNumber.length - 4);
    return start + '****' + end;
  }
  return phoneNumber;
}

async function sendSMS(phoneNumber: string, code: string, type: string): Promise<{ success: boolean; error?: string }> {
  try {
    // Mock SMS implementation - in production, integrate with SMS providers
    // Popular SMS services in Cameroon: MTN, Orange, Nexttel
    
    const message = getSMSMessage(code, type);
    
    // Mock successful SMS sending
    console.log(`SMS sent to ${phoneNumber}: ${message}`);
    
    // In production, integrate with SMS gateway:
    // - MTN SMS API
    // - Orange SMS API
    // - Twilio (international)
    // - Africa's Talking
    // - Nexmo/Vonage
    
    return { success: true };
    
  } catch (error) {
    console.error('SMS sending error:', error);
    return { 
      success: false, 
      error: 'Failed to send SMS' 
    };
  }
}

function getSMSMessage(code: string, type: string): string {
  const messages = {
    registration: `GCE Registration: Your verification code is ${code}. Valid for 10 minutes. Do not share this code.`,
    login: `GCE Login: Your verification code is ${code}. Valid for 10 minutes.`,
    password_reset: `GCE Password Reset: Your verification code is ${code}. Valid for 10 minutes.`
  };
  
  return messages[type as keyof typeof messages] || messages.registration;
}
