#!/usr/bin/env tsx
/**
 * Database Backup Script
 * Creates comprehensive backups of the GCE System database
 */

import { PrismaClient } from '../src/generated/prisma';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const prisma = new PrismaClient();

interface BackupConfig {
  includeData: boolean;
  includeSchema: boolean;
  compress: boolean;
  outputDir: string;
  format: 'sql' | 'json' | 'both';
}

interface BackupResult {
  timestamp: string;
  files: string[];
  size: number;
  duration: number;
  success: boolean;
  error?: string;
}

async function createBackupDirectory(outputDir: string): Promise<string> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(outputDir, `backup-${timestamp}`);
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  return backupDir;
}

async function backupWithPgDump(backupDir: string, config: BackupConfig): Promise<string[]> {
  const files: string[] = [];
  const dbUrl = process.env.DATABASE_URL;
  
  if (!dbUrl || !dbUrl.includes('postgresql://')) {
    throw new Error('PostgreSQL DATABASE_URL not found or invalid');
  }

  // Parse connection details from DATABASE_URL
  const url = new URL(dbUrl);
  const host = url.hostname;
  const port = url.port || '5432';
  const database = url.pathname.slice(1);
  const username = url.username;
  const password = url.password;

  // Set environment variable for password
  process.env.PGPASSWORD = password;

  try {
    if (config.includeSchema) {
      // Schema-only backup
      const schemaFile = path.join(backupDir, 'schema.sql');
      const schemaCmd = `pg_dump -h ${host} -p ${port} -U ${username} -d ${database} --schema-only -f "${schemaFile}"`;
      
      console.log('📋 Creating schema backup...');
      await execAsync(schemaCmd);
      files.push(schemaFile);
      console.log(`✅ Schema backup created: ${schemaFile}`);
    }

    if (config.includeData) {
      // Data-only backup
      const dataFile = path.join(backupDir, 'data.sql');
      const dataCmd = `pg_dump -h ${host} -p ${port} -U ${username} -d ${database} --data-only -f "${dataFile}"`;
      
      console.log('📊 Creating data backup...');
      await execAsync(dataCmd);
      files.push(dataFile);
      console.log(`✅ Data backup created: ${dataFile}`);

      // Full backup (schema + data)
      const fullFile = path.join(backupDir, 'full-backup.sql');
      const fullCmd = `pg_dump -h ${host} -p ${port} -U ${username} -d ${database} -f "${fullFile}"`;
      
      console.log('🗄️ Creating full backup...');
      await execAsync(fullCmd);
      files.push(fullFile);
      console.log(`✅ Full backup created: ${fullFile}`);
    }

    // Custom format backup (compressed)
    if (config.compress) {
      const customFile = path.join(backupDir, 'backup.dump');
      const customCmd = `pg_dump -h ${host} -p ${port} -U ${username} -d ${database} -Fc -f "${customFile}"`;
      
      console.log('🗜️ Creating compressed backup...');
      await execAsync(customCmd);
      files.push(customFile);
      console.log(`✅ Compressed backup created: ${customFile}`);
    }

  } finally {
    // Clean up password environment variable
    delete process.env.PGPASSWORD;
  }

  return files;
}

async function backupWithPrisma(backupDir: string): Promise<string[]> {
  const files: string[] = [];
  
  console.log('📊 Creating Prisma-based JSON backup...');

  try {
    // Backup all tables to JSON
    const backupData: any = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      data: {}
    };

    // Public schema tables
    console.log('   Backing up subjects...');
    backupData.data.subjects = await prisma.subject.findMany();
    
    console.log('   Backing up schools...');
    backupData.data.schools = await prisma.school.findMany();
    
    console.log('   Backing up exam centers...');
    backupData.data.examCenters = await prisma.examCenter.findMany();
    
    console.log('   Backing up exam sessions...');
    backupData.data.examSessions = await prisma.examSession.findMany();
    
    console.log('   Backing up school pre-registrations...');
    backupData.data.schoolPreRegistrations = await prisma.schoolPreRegistration.findMany();
    
    console.log('   Backing up registration invitations...');
    backupData.data.registrationInvitations = await prisma.registrationInvitation.findMany();
    
    console.log('   Backing up audit logs...');
    backupData.data.auditLogs = await prisma.auditLog.findMany({
      take: 10000, // Limit audit logs to prevent huge files
      orderBy: { timestamp: 'desc' }
    });

    // User data (separate schemas)
    try {
      console.log('   Backing up O Level students...');
      backupData.data.oLevelStudents = await prisma.oLevelStudent.findMany();
    } catch (error) {
      console.log('   ⚠️ O Level students table not accessible or empty');
    }

    try {
      console.log('   Backing up A Level students...');
      backupData.data.aLevelStudents = await prisma.aLevelStudent.findMany();
    } catch (error) {
      console.log('   ⚠️ A Level students table not accessible or empty');
    }

    try {
      console.log('   Backing up teachers...');
      backupData.data.teachers = await prisma.teacherUser.findMany();
    } catch (error) {
      console.log('   ⚠️ Teachers table not accessible or empty');
    }

    try {
      console.log('   Backing up examiners...');
      backupData.data.examiners = await prisma.examinerUser.findMany();
    } catch (error) {
      console.log('   ⚠️ Examiners table not accessible or empty');
    }

    // Save JSON backup
    const jsonFile = path.join(backupDir, 'prisma-backup.json');
    fs.writeFileSync(jsonFile, JSON.stringify(backupData, null, 2));
    files.push(jsonFile);
    
    console.log(`✅ Prisma JSON backup created: ${jsonFile}`);

    // Create metadata file
    const metadataFile = path.join(backupDir, 'backup-metadata.json');
    const metadata = {
      timestamp: new Date().toISOString(),
      method: 'prisma',
      recordCounts: Object.fromEntries(
        Object.entries(backupData.data).map(([table, data]: [string, any]) => [
          table,
          Array.isArray(data) ? data.length : 0
        ])
      ),
      totalRecords: Object.values(backupData.data).reduce(
        (total, data: any) => total + (Array.isArray(data) ? data.length : 0),
        0
      )
    };
    
    fs.writeFileSync(metadataFile, JSON.stringify(metadata, null, 2));
    files.push(metadataFile);

  } catch (error) {
    console.error('❌ Error creating Prisma backup:', error);
    throw error;
  }

  return files;
}

async function calculateBackupSize(files: string[]): Promise<number> {
  let totalSize = 0;
  
  for (const file of files) {
    try {
      const stats = fs.statSync(file);
      totalSize += stats.size;
    } catch (error) {
      console.warn(`Warning: Could not get size for ${file}`);
    }
  }
  
  return totalSize;
}

async function createBackup(config: BackupConfig): Promise<BackupResult> {
  const startTime = Date.now();
  const result: BackupResult = {
    timestamp: new Date().toISOString(),
    files: [],
    size: 0,
    duration: 0,
    success: false
  };

  try {
    console.log('🗄️ Starting database backup...');
    console.log(`📁 Output directory: ${config.outputDir}`);
    console.log(`📋 Include schema: ${config.includeSchema}`);
    console.log(`📊 Include data: ${config.includeData}`);
    console.log(`🗜️ Compress: ${config.compress}`);
    console.log(`📄 Format: ${config.format}\n`);

    const backupDir = await createBackupDirectory(config.outputDir);
    console.log(`📁 Created backup directory: ${backupDir}\n`);

    // Connect to database
    await prisma.$connect();

    let files: string[] = [];

    if (config.format === 'sql' || config.format === 'both') {
      const sqlFiles = await backupWithPgDump(backupDir, config);
      files.push(...sqlFiles);
    }

    if (config.format === 'json' || config.format === 'both') {
      const jsonFiles = await backupWithPrisma(backupDir);
      files.push(...jsonFiles);
    }

    result.files = files;
    result.size = await calculateBackupSize(files);
    result.duration = Date.now() - startTime;
    result.success = true;

    console.log(`\n✅ Backup completed successfully!`);
    console.log(`📁 Location: ${backupDir}`);
    console.log(`📄 Files: ${files.length}`);
    console.log(`💾 Size: ${(result.size / 1024 / 1024).toFixed(2)} MB`);
    console.log(`⏱️ Duration: ${(result.duration / 1000).toFixed(2)}s`);

  } catch (error) {
    result.error = error instanceof Error ? error.message : String(error);
    result.success = false;
    console.error('❌ Backup failed:', error);
  } finally {
    await prisma.$disconnect();
  }

  return result;
}

async function main() {
  const args = process.argv.slice(2);
  
  const config: BackupConfig = {
    includeData: !args.includes('--schema-only'),
    includeSchema: !args.includes('--data-only'),
    compress: args.includes('--compress'),
    outputDir: args.find(arg => arg.startsWith('--output='))?.split('=')[1] || './backups',
    format: (args.find(arg => arg.startsWith('--format='))?.split('=')[1] as any) || 'both'
  };

  if (args.includes('--help')) {
    console.log(`
Database Backup Tool

Usage: npm run db:backup [options]

Options:
  --schema-only     Backup schema only (no data)
  --data-only       Backup data only (no schema)
  --compress        Create compressed backup
  --output=<dir>    Output directory (default: ./backups)
  --format=<fmt>    Format: sql, json, or both (default: both)
  --help            Show this help message

Examples:
  npm run db:backup
  npm run db:backup -- --compress --output=./my-backups
  npm run db:backup -- --schema-only --format=sql
    `);
    return;
  }

  await createBackup(config);
}

if (require.main === module) {
  main();
}

export { createBackup };
