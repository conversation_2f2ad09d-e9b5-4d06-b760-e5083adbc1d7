import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/postgresDb';
import { logRegistrationAttempt, assessRegistrationRisk, PROFESSIONAL_REQUIREMENTS, PROFESSIONAL_MESSAGES } from '@/lib/secureRegistration';

// GET /api/professional-registration - Get professional registration applications
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const applicantType = searchParams.get('applicantType');
    const status = searchParams.get('status');
    const email = searchParams.get('email');

    // Build where clause
    const whereClause: any = {};
    if (applicantType) whereClause.applicantType = applicantType;
    if (status) whereClause.status = status;
    if (email) whereClause.email = email.toLowerCase();

    const registrations = await prisma.professionalRegistration.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        applicantType: true,
        fullName: true,
        email: true,
        currentEmployer: true,
        status: true,
        submittedAt: true,
        reviewedAt: true,
        approvedAt: true,
        accountCreated: true
      }
    });

    return NextResponse.json({
      success: true,
      data: registrations,
      message: 'Professional registrations retrieved successfully'
    });

  } catch (error) {
    console.error('Get professional registrations error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/professional-registration - Submit professional registration application
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      applicantType,
      personalInfo,
      professionalInfo,
      institutionInfo,
      documents,
      references
    } = body;

    // Get IP and user agent for security logging
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || '';

    // Validate required fields
    if (!applicantType || !personalInfo || !professionalInfo) {
      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'professional_registration',
        false,
        'Missing required fields'
      );

      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate applicant type
    if (!['teacher', 'examiner'].includes(applicantType)) {
      return NextResponse.json(
        { success: false, message: 'Invalid applicant type' },
        { status: 400 }
      );
    }

    // Check for existing application
    const existingApplication = await prisma.professionalRegistration.findFirst({
      where: {
        email: personalInfo.email.toLowerCase(),
        status: { in: ['pending', 'under_review', 'approved'] }
      }
    });

    if (existingApplication) {
      return NextResponse.json(
        { success: false, message: 'An active application already exists for this email' },
        { status: 409 }
      );
    }

    // Risk assessment
    const riskAssessment = await assessRegistrationRisk(
      ipAddress,
      userAgent,
      personalInfo.email,
      personalInfo.nationalId
    );

    if (riskAssessment.recommendation === 'reject') {
      await logRegistrationAttempt(
        ipAddress,
        userAgent,
        'professional_registration',
        false,
        'High risk score',
        personalInfo.email,
        personalInfo.nationalId
      );

      return NextResponse.json(
        { success: false, message: 'Application cannot be processed due to security concerns' },
        { status: 403 }
      );
    }

    // Validate required documents
    const requirements = applicantType === 'teacher' 
      ? PROFESSIONAL_REQUIREMENTS.TEACHER 
      : PROFESSIONAL_REQUIREMENTS.EXAMINER;

    const missingDocuments = requirements.REQUIRED_DOCUMENTS.filter(
      doc => !documents || !documents[doc]
    );

    if (missingDocuments.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Missing required documents: ' + missingDocuments.join(', '),
          missingDocuments 
        },
        { status: 400 }
      );
    }

    // Create professional registration
    const registration = await prisma.professionalRegistration.create({
      data: {
        applicantType,
        fullName: personalInfo.fullName,
        email: personalInfo.email.toLowerCase(),
        nationalId: personalInfo.nationalId,
        phoneNumber: personalInfo.phoneNumber,
        dateOfBirth: personalInfo.dateOfBirth,
        gender: personalInfo.gender,
        region: personalInfo.region,
        
        qualifications: professionalInfo.qualifications || [],
        experience: professionalInfo.experience || [],
        specializations: professionalInfo.specializations || [],
        
        currentEmployer: institutionInfo?.currentEmployer,
        employerType: institutionInfo?.employerType,
        employmentStatus: institutionInfo?.employmentStatus,
        yearsOfService: institutionInfo?.yearsOfService,
        
        // Teacher-specific fields
        teachingLicense: applicantType === 'teacher' ? professionalInfo.teachingLicense : null,
        schoolId: applicantType === 'teacher' ? institutionInfo?.schoolId : null,
        subjectsTaught: applicantType === 'teacher' ? professionalInfo.subjectsTaught : null,
        
        // Examiner-specific fields
        examinerLevel: applicantType === 'examiner' ? professionalInfo.examinerLevel : null,
        examiningSubjects: applicantType === 'examiner' ? professionalInfo.examiningSubjects : null,
        previousExamining: applicantType === 'examiner' ? professionalInfo.previousExamining : null,
        
        documents: documents || {},
        references: references || [],
        
        status: 'pending'
      }
    });

    // Create institution verification for teachers
    if (applicantType === 'teacher' && institutionInfo) {
      await prisma.institutionVerification.create({
        data: {
          registrationId: registration.id,
          institutionName: institutionInfo.currentEmployer,
          institutionType: institutionInfo.institutionType || 'secondary',
          institutionCode: institutionInfo.institutionCode,
          region: personalInfo.region,
          division: institutionInfo.division,
          contactPerson: institutionInfo.contactPerson,
          contactPhone: institutionInfo.contactPhone,
          contactEmail: institutionInfo.contactEmail,
          status: 'pending'
        }
      });
    }

    // Create examiner approval record for examiners
    if (applicantType === 'examiner') {
      await prisma.examinerApproval.create({
        data: {
          registrationId: registration.id,
          approvalLevel: 'national',
          approvalBody: 'Ministry of Education',
          qualificationLevel: 'under_assessment',
          subjectCompetency: professionalInfo.examiningSubjects || [],
          securityLevel: 'basic',
          status: 'pending'
        }
      });
    }

    // Log successful application
    await logRegistrationAttempt(
      ipAddress,
      userAgent,
      'professional_registration',
      true,
      undefined,
      personalInfo.email,
      personalInfo.nationalId
    );

    const successMessage = applicantType === 'teacher' 
      ? PROFESSIONAL_MESSAGES.TEACHER_APPLICATION_SUBMITTED
      : PROFESSIONAL_MESSAGES.EXAMINER_APPLICATION_SUBMITTED;

    return NextResponse.json({
      success: true,
      data: {
        id: registration.id,
        applicantType: registration.applicantType,
        status: registration.status,
        submittedAt: registration.submittedAt
      },
      message: successMessage
    });

  } catch (error) {
    console.error('Professional registration error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/professional-registration - Update application status (admin only)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, status, reviewNotes, approvedBy } = body;

    if (!id || !status) {
      return NextResponse.json(
        { success: false, message: 'Registration ID and status are required' },
        { status: 400 }
      );
    }

    // Validate status
    const validStatuses = ['pending', 'under_review', 'approved', 'rejected', 'suspended'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, message: 'Invalid status' },
        { status: 400 }
      );
    }

    const updateData: any = { status };

    if (status === 'under_review') {
      updateData.reviewedAt = new Date();
      updateData.reviewedBy = approvedBy;
    } else if (status === 'approved') {
      updateData.approvedAt = new Date();
      updateData.approvedBy = approvedBy;
    } else if (status === 'rejected') {
      updateData.rejectionReason = reviewNotes;
    }

    const updatedRegistration = await prisma.professionalRegistration.update({
      where: { id },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: updatedRegistration,
      message: 'Registration status updated successfully'
    });

  } catch (error) {
    console.error('Update professional registration error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
