# 🚀 Production Deployment Guide

## 📋 Overview

This guide covers the complete production deployment process for the GCE System, including infrastructure setup, security configuration, monitoring, and maintenance procedures.

## 🎯 Deployment Architecture

### Production Infrastructure
```
Internet
    ↓
[Load Balancer/CDN]
    ↓
[Nginx Reverse Proxy]
    ↓
[App Instances (2x)]
    ↓
[Database Cluster] ← [Redis Cache]
    ↓
[Monitoring Stack]
```

### Components
- **Application**: Next.js with Node.js runtime
- **Database**: PostgreSQL with replication
- **Cache**: Redis cluster
- **Reverse Proxy**: Nginx with SSL termination
- **Monitoring**: Prometheus + Grafana
- **Logging**: Fluentd + ELK Stack
- **Backup**: Automated database and file backups

## 🛠️ Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04 LTS or CentOS 8+
- **CPU**: 4+ cores (8+ recommended)
- **RAM**: 8GB minimum (16GB+ recommended)
- **Storage**: 100GB+ SSD
- **Network**: Static IP with domain name

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git
- SSL certificates
- Domain name with DNS configuration

## 🔧 Pre-Deployment Setup

### 1. Server Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create application user
sudo useradd -m -s /bin/bash gce-system
sudo usermod -aG docker gce-system
```

### 2. SSL Certificate Setup
```bash
# Using Let's Encrypt (recommended)
sudo apt install certbot
sudo certbot certonly --standalone -d gce.cm -d www.gce.cm

# Copy certificates to project
sudo cp /etc/letsencrypt/live/gce.cm/fullchain.pem config/ssl/gce.cm.crt
sudo cp /etc/letsencrypt/live/gce.cm/privkey.pem config/ssl/gce.cm.key
sudo chown gce-system:gce-system config/ssl/*
```

### 3. Environment Configuration
```bash
# Create production environment file
cp .env.example .env.production

# Edit with production values
nano .env.production
```

### 4. DNS Configuration
```
# A Records
gce.cm.        IN  A   YOUR_SERVER_IP
www.gce.cm.    IN  A   YOUR_SERVER_IP

# CNAME Records (optional)
api.gce.cm.    IN  CNAME  gce.cm.
admin.gce.cm.  IN  CNAME  gce.cm.
```

## 🚀 Deployment Process

### 1. Clone Repository
```bash
# Switch to application user
sudo su - gce-system

# Clone repository
git clone https://github.com/your-org/gce-system.git
cd gce-system

# Checkout production branch
git checkout production
```

### 2. Configure Environment
```bash
# Copy and edit production environment
cp .env.example .env.production
nano .env.production
```

#### Required Environment Variables
```env
# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://gce.cm

# Database
DATABASE_URL=***************************************************/gce_system
POSTGRES_DB=gce_system
POSTGRES_USER=gce_user
POSTGRES_PASSWORD=secure_password

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis_secure_password

# Security
JWT_SECRET=your_super_secure_jwt_secret_here
BCRYPT_ROUNDS=12
CERTIFICATE_SIGNING_KEY=your_certificate_signing_key

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# Monitoring
GRAFANA_ADMIN_PASSWORD=secure_grafana_password
GRAFANA_SECRET_KEY=grafana_secret_key

# Backup
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BACKUP_BUCKET=gce-system-backups
```

### 3. Deploy Application
```bash
# Make deployment script executable
chmod +x scripts/deploy-production.sh

# Run deployment
./scripts/deploy-production.sh deploy
```

### 4. Verify Deployment
```bash
# Check container status
docker-compose -f docker-compose.prod.yml ps

# Check application health
curl -f https://gce.cm/health

# Check logs
docker-compose -f docker-compose.prod.yml logs -f app1
```

## 📊 Monitoring Setup

### 1. Access Monitoring Dashboards
- **Grafana**: https://gce.cm/grafana
- **Prometheus**: https://gce.cm/prometheus
- **Application Logs**: `docker-compose logs -f`

### 2. Configure Alerts
```bash
# Edit Prometheus alerting rules
nano config/prometheus-alerts.yml

# Restart Prometheus
docker-compose -f docker-compose.prod.yml restart prometheus
```

### 3. Set Up Log Aggregation
```bash
# Configure Fluentd
nano config/fluentd.conf

# Restart log aggregation
docker-compose -f docker-compose.prod.yml restart fluentd
```

## 🔒 Security Configuration

### 1. Firewall Setup
```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 2. Security Headers
- Configured in Nginx (see `config/nginx-prod.conf`)
- HSTS, CSP, X-Frame-Options, etc.

### 3. Database Security
```sql
-- Create read-only user for monitoring
CREATE USER monitoring WITH PASSWORD 'monitoring_password';
GRANT CONNECT ON DATABASE gce_system TO monitoring;
GRANT USAGE ON SCHEMA public TO monitoring;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO monitoring;
```

## 💾 Backup Strategy

### 1. Automated Backups
- **Database**: Daily full backup + 6-hourly incremental
- **Files**: Daily backup of uploads and certificates
- **Configuration**: Weekly backup of configuration files

### 2. Manual Backup
```bash
# Create manual backup
./scripts/deploy-production.sh backup

# List backups
ls -la backups/
```

### 3. Restore from Backup
```bash
# Restore specific backup
./scripts/restore-backup.sh backup-20241208-120000
```

## 🔄 Maintenance Procedures

### 1. Regular Updates
```bash
# Update application
git pull origin production
./scripts/deploy-production.sh deploy

# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Docker images
docker-compose -f docker-compose.prod.yml pull
```

### 2. SSL Certificate Renewal
```bash
# Renew Let's Encrypt certificates
sudo certbot renew --dry-run
sudo certbot renew

# Update certificates in application
sudo cp /etc/letsencrypt/live/gce.cm/fullchain.pem config/ssl/gce.cm.crt
sudo cp /etc/letsencrypt/live/gce.cm/privkey.pem config/ssl/gce.cm.key
docker-compose -f docker-compose.prod.yml restart nginx
```

### 3. Database Maintenance
```bash
# Database vacuum and analyze
docker-compose -f docker-compose.prod.yml exec postgres psql -U gce_user -d gce_system -c "VACUUM ANALYZE;"

# Check database size
docker-compose -f docker-compose.prod.yml exec postgres psql -U gce_user -d gce_system -c "SELECT pg_size_pretty(pg_database_size('gce_system'));"
```

## 🚨 Troubleshooting

### 1. Application Issues
```bash
# Check application logs
docker-compose -f docker-compose.prod.yml logs app1 app2

# Check container health
docker-compose -f docker-compose.prod.yml ps

# Restart application
docker-compose -f docker-compose.prod.yml restart app1 app2
```

### 2. Database Issues
```bash
# Check database logs
docker-compose -f docker-compose.prod.yml logs postgres

# Check database connections
docker-compose -f docker-compose.prod.yml exec postgres psql -U gce_user -d gce_system -c "SELECT count(*) FROM pg_stat_activity;"

# Restart database
docker-compose -f docker-compose.prod.yml restart postgres
```

### 3. Performance Issues
```bash
# Check resource usage
docker stats

# Check system resources
htop
df -h
free -h

# Analyze slow queries
docker-compose -f docker-compose.prod.yml exec postgres psql -U gce_user -d gce_system -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"
```

## 🔄 Rollback Procedures

### 1. Quick Rollback
```bash
# Rollback to previous deployment
./scripts/deploy-production.sh rollback
```

### 2. Manual Rollback
```bash
# Stop current deployment
docker-compose -f docker-compose.prod.yml down

# Restore from specific backup
./scripts/restore-backup.sh backup-20241208-120000

# Start application
docker-compose -f docker-compose.prod.yml up -d
```

## 📈 Scaling Strategies

### 1. Horizontal Scaling
```bash
# Add more application instances
docker-compose -f docker-compose.prod.yml up -d --scale app1=3 --scale app2=3
```

### 2. Database Scaling
- Set up read replicas
- Implement connection pooling
- Optimize queries and indexes

### 3. Load Balancing
- Configure external load balancer
- Use CDN for static assets
- Implement auto-scaling

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] Server provisioned and configured
- [ ] SSL certificates obtained and installed
- [ ] DNS records configured
- [ ] Environment variables set
- [ ] Backup strategy implemented
- [ ] Monitoring configured

### Deployment
- [ ] Code deployed from production branch
- [ ] Database migrations applied
- [ ] Application containers started
- [ ] Health checks passing
- [ ] SSL/HTTPS working
- [ ] Monitoring dashboards accessible

### Post-Deployment
- [ ] Performance testing completed
- [ ] Security scan performed
- [ ] Backup verification
- [ ] Documentation updated
- [ ] Team notified

## 🆘 Emergency Procedures

### 1. System Down
1. Check container status
2. Review logs for errors
3. Restart affected services
4. If critical, rollback to last known good state
5. Notify stakeholders

### 2. Data Corruption
1. Stop application immediately
2. Assess extent of corruption
3. Restore from latest backup
4. Verify data integrity
5. Resume operations

### 3. Security Breach
1. Isolate affected systems
2. Change all passwords and secrets
3. Review access logs
4. Apply security patches
5. Conduct security audit

---

## ✅ Production Deployment Checklist

- [x] Docker containerization
- [x] Production Docker Compose configuration
- [x] Nginx reverse proxy with SSL
- [x] Automated deployment script
- [x] Database backup and restore
- [x] Monitoring and logging
- [x] Security configuration
- [x] Health checks
- [x] Rollback procedures
- [x] Maintenance documentation
- [x] Troubleshooting guide
- [x] Emergency procedures

**🎉 Your GCE System is ready for production deployment!**
