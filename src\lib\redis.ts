/**
 * Redis Integration for Production Caching
 * Provides distributed caching, session management, and rate limiting
 */

import { createClient, RedisClientType } from 'redis';

interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
  maxRetries: number;
  retryDelay: number;
}

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  compress?: boolean;
  serialize?: boolean;
}

/**
 * Redis Cache Manager
 */
export class RedisManager {
  private static client: RedisClientType | null = null;
  private static isConnected = false;
  private static config: RedisConfig;

  /**
   * Initialize Redis connection
   */
  static async initialize(config?: Partial<RedisConfig>): Promise<void> {
    this.config = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      maxRetries: 3,
      retryDelay: 1000,
      ...config
    };

    try {
      this.client = createClient({
        socket: {
          host: this.config.host,
          port: this.config.port,
          reconnectStrategy: (retries) => {
            if (retries > this.config.maxRetries) {
              return new Error('Max retries exceeded');
            }
            return Math.min(retries * this.config.retryDelay, 3000);
          }
        },
        password: this.config.password,
        database: this.config.db
      });

      this.client.on('error', (err) => {
        console.error('Redis Client Error:', err);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('✅ Redis connected');
        this.isConnected = true;
      });

      this.client.on('disconnect', () => {
        console.log('❌ Redis disconnected');
        this.isConnected = false;
      });

      await this.client.connect();
      console.log('🔗 Redis client initialized');

    } catch (error) {
      console.error('Failed to initialize Redis:', error);
      throw error;
    }
  }

  /**
   * Get Redis client
   */
  static getClient(): RedisClientType {
    if (!this.client) {
      throw new Error('Redis client not initialized. Call initialize() first.');
    }
    return this.client;
  }

  /**
   * Check if Redis is connected
   */
  static isReady(): boolean {
    return this.isConnected && this.client?.isReady === true;
  }

  /**
   * Set cache value
   */
  static async set(
    key: string, 
    value: any, 
    options: CacheOptions = {}
  ): Promise<boolean> {
    if (!this.isReady()) {
      console.warn('Redis not available, skipping cache set');
      return false;
    }

    try {
      const client = this.getClient();
      let serializedValue: string;

      if (options.serialize !== false) {
        serializedValue = JSON.stringify(value);
      } else {
        serializedValue = String(value);
      }

      if (options.compress) {
        // In production, use compression library like zlib
        // serializedValue = await compress(serializedValue);
      }

      if (options.ttl) {
        await client.setEx(key, options.ttl, serializedValue);
      } else {
        await client.set(key, serializedValue);
      }

      return true;
    } catch (error) {
      console.error('Redis set error:', error);
      return false;
    }
  }

  /**
   * Get cache value
   */
  static async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    if (!this.isReady()) {
      return null;
    }

    try {
      const client = this.getClient();
      let value = await client.get(key);

      if (value === null) {
        return null;
      }

      if (options.compress) {
        // In production, decompress the value
        // value = await decompress(value);
      }

      if (options.serialize !== false) {
        return JSON.parse(value) as T;
      } else {
        return value as T;
      }
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  /**
   * Delete cache value
   */
  static async delete(key: string): Promise<boolean> {
    if (!this.isReady()) {
      return false;
    }

    try {
      const client = this.getClient();
      const result = await client.del(key);
      return result > 0;
    } catch (error) {
      console.error('Redis delete error:', error);
      return false;
    }
  }

  /**
   * Check if key exists
   */
  static async exists(key: string): Promise<boolean> {
    if (!this.isReady()) {
      return false;
    }

    try {
      const client = this.getClient();
      const result = await client.exists(key);
      return result > 0;
    } catch (error) {
      console.error('Redis exists error:', error);
      return false;
    }
  }

  /**
   * Set expiration for key
   */
  static async expire(key: string, seconds: number): Promise<boolean> {
    if (!this.isReady()) {
      return false;
    }

    try {
      const client = this.getClient();
      const result = await client.expire(key, seconds);
      return result;
    } catch (error) {
      console.error('Redis expire error:', error);
      return false;
    }
  }

  /**
   * Get multiple keys
   */
  static async mget<T>(keys: string[]): Promise<(T | null)[]> {
    if (!this.isReady() || keys.length === 0) {
      return keys.map(() => null);
    }

    try {
      const client = this.getClient();
      const values = await client.mGet(keys);
      
      return values.map(value => {
        if (value === null) return null;
        try {
          return JSON.parse(value) as T;
        } catch {
          return value as T;
        }
      });
    } catch (error) {
      console.error('Redis mget error:', error);
      return keys.map(() => null);
    }
  }

  /**
   * Set multiple keys
   */
  static async mset(keyValues: Record<string, any>, ttl?: number): Promise<boolean> {
    if (!this.isReady()) {
      return false;
    }

    try {
      const client = this.getClient();
      const serializedKeyValues: Record<string, string> = {};

      for (const [key, value] of Object.entries(keyValues)) {
        serializedKeyValues[key] = JSON.stringify(value);
      }

      await client.mSet(serializedKeyValues);

      if (ttl) {
        const promises = Object.keys(keyValues).map(key => 
          client.expire(key, ttl)
        );
        await Promise.all(promises);
      }

      return true;
    } catch (error) {
      console.error('Redis mset error:', error);
      return false;
    }
  }

  /**
   * Increment counter
   */
  static async increment(key: string, by: number = 1): Promise<number | null> {
    if (!this.isReady()) {
      return null;
    }

    try {
      const client = this.getClient();
      return await client.incrBy(key, by);
    } catch (error) {
      console.error('Redis increment error:', error);
      return null;
    }
  }

  /**
   * Get keys by pattern
   */
  static async keys(pattern: string): Promise<string[]> {
    if (!this.isReady()) {
      return [];
    }

    try {
      const client = this.getClient();
      return await client.keys(pattern);
    } catch (error) {
      console.error('Redis keys error:', error);
      return [];
    }
  }

  /**
   * Clear all cache
   */
  static async clear(): Promise<boolean> {
    if (!this.isReady()) {
      return false;
    }

    try {
      const client = this.getClient();
      await client.flushDb();
      return true;
    } catch (error) {
      console.error('Redis clear error:', error);
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  static async getStats(): Promise<{
    connected: boolean;
    memory: string;
    keys: number;
    hits: number;
    misses: number;
    hitRate: number;
  }> {
    if (!this.isReady()) {
      return {
        connected: false,
        memory: '0',
        keys: 0,
        hits: 0,
        misses: 0,
        hitRate: 0
      };
    }

    try {
      const client = this.getClient();
      const info = await client.info('memory');
      const stats = await client.info('stats');
      const dbSize = await client.dbSize();

      // Parse memory info
      const memoryMatch = info.match(/used_memory_human:(.+)/);
      const memory = memoryMatch ? memoryMatch[1].trim() : '0';

      // Parse stats
      const hitsMatch = stats.match(/keyspace_hits:(\d+)/);
      const missesMatch = stats.match(/keyspace_misses:(\d+)/);
      
      const hits = hitsMatch ? parseInt(hitsMatch[1]) : 0;
      const misses = missesMatch ? parseInt(missesMatch[1]) : 0;
      const hitRate = hits + misses > 0 ? hits / (hits + misses) : 0;

      return {
        connected: true,
        memory,
        keys: dbSize,
        hits,
        misses,
        hitRate: Math.round(hitRate * 100) / 100
      };
    } catch (error) {
      console.error('Redis stats error:', error);
      return {
        connected: false,
        memory: '0',
        keys: 0,
        hits: 0,
        misses: 0,
        hitRate: 0
      };
    }
  }

  /**
   * Health check
   */
  static async healthCheck(): Promise<{
    healthy: boolean;
    responseTime: number;
    error?: string;
  }> {
    const startTime = Date.now();

    if (!this.isReady()) {
      return {
        healthy: false,
        responseTime: Date.now() - startTime,
        error: 'Redis not connected'
      };
    }

    try {
      const client = this.getClient();
      await client.ping();
      
      return {
        healthy: true,
        responseTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        healthy: false,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Close connection
   */
  static async close(): Promise<void> {
    if (this.client) {
      await this.client.quit();
      this.client = null;
      this.isConnected = false;
      console.log('🔌 Redis connection closed');
    }
  }
}

/**
 * Session Management with Redis
 */
export class RedisSessionManager {
  private static sessionPrefix = 'session:';
  private static defaultTTL = 24 * 60 * 60; // 24 hours

  /**
   * Create session
   */
  static async createSession(
    sessionId: string, 
    data: any, 
    ttl: number = this.defaultTTL
  ): Promise<boolean> {
    const key = this.sessionPrefix + sessionId;
    return await RedisManager.set(key, data, { ttl });
  }

  /**
   * Get session
   */
  static async getSession<T>(sessionId: string): Promise<T | null> {
    const key = this.sessionPrefix + sessionId;
    return await RedisManager.get<T>(key);
  }

  /**
   * Update session
   */
  static async updateSession(
    sessionId: string, 
    data: any, 
    ttl: number = this.defaultTTL
  ): Promise<boolean> {
    const key = this.sessionPrefix + sessionId;
    return await RedisManager.set(key, data, { ttl });
  }

  /**
   * Delete session
   */
  static async deleteSession(sessionId: string): Promise<boolean> {
    const key = this.sessionPrefix + sessionId;
    return await RedisManager.delete(key);
  }

  /**
   * Extend session TTL
   */
  static async extendSession(sessionId: string, ttl: number = this.defaultTTL): Promise<boolean> {
    const key = this.sessionPrefix + sessionId;
    return await RedisManager.expire(key, ttl);
  }
}

/**
 * Rate Limiting with Redis
 */
export class RedisRateLimiter {
  /**
   * Check rate limit using sliding window
   */
  static async checkRateLimit(
    key: string,
    windowMs: number,
    maxRequests: number
  ): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }> {
    if (!RedisManager.isReady()) {
      // Fallback to allow if Redis is not available
      return {
        allowed: true,
        remaining: maxRequests - 1,
        resetTime: Date.now() + windowMs
      };
    }

    try {
      const client = RedisManager.getClient();
      const now = Date.now();
      const window = Math.floor(now / windowMs);
      const rateLimitKey = `rate_limit:${key}:${window}`;

      const current = await client.incr(rateLimitKey);
      
      if (current === 1) {
        // First request in this window, set expiration
        await client.expire(rateLimitKey, Math.ceil(windowMs / 1000));
      }

      const remaining = Math.max(0, maxRequests - current);
      const resetTime = (window + 1) * windowMs;

      return {
        allowed: current <= maxRequests,
        remaining,
        resetTime
      };
    } catch (error) {
      console.error('Redis rate limit error:', error);
      // Fallback to allow on error
      return {
        allowed: true,
        remaining: maxRequests - 1,
        resetTime: Date.now() + windowMs
      };
    }
  }
}

export { RedisManager, RedisSessionManager, RedisRateLimiter };
