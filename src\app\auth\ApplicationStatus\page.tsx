"use client";

import { useState } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';

export default function ApplicationStatus() {
  const [email, setEmail] = useState('');
  const [applicantType, setApplicantType] = useState('teacher');
  const [loading, setLoading] = useState(false);
  const [applicationData, setApplicationData] = useState<any>(null);
  const [error, setError] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();

  // Pre-fill from URL params if available
  useState(() => {
    const emailParam = searchParams.get('email');
    const typeParam = searchParams.get('type');
    if (emailParam) setEmail(emailParam);
    if (typeParam && ['teacher', 'examiner'].includes(typeParam)) {
      setApplicantType(typeParam);
    }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setApplicationData(null);

    try {
      const response = await fetch(`/api/professional-status?email=${encodeURIComponent(email)}&applicantType=${applicantType}`);
      const result = await response.json();

      if (result.success) {
        setApplicationData(result.data);
      } else {
        setError(result.message || 'Application not found');
      }
    } catch (err) {
      console.error('Status check error:', err);
      setError('Network error. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'under_review': return 'text-orange-600 bg-orange-100';
      case 'pending': return 'text-blue-600 bg-blue-100';
      case 'suspended': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return (
          <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'rejected':
        return (
          <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'under_review':
        return (
          <svg className="h-8 w-8 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        );
      case 'pending':
        return (
          <svg className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return (
          <svg className="h-8 w-8 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center p-1 shadow-sm">
                <img
                  src="/images/GCEB.png"
                  alt="GCE Board Logo"
                  className="w-full h-full object-contain"
                />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">GCE Board</h1>
                <p className="text-xs text-gray-500">Professional Application Status</p>
              </div>
            </div>
            <Link href="/auth/Login" className="text-blue-600 hover:text-blue-700 font-medium">
              Back to Login
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl mx-auto">
          {/* Title */}
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-3">Check Application Status</h2>
            <p className="text-lg text-gray-600">
              Enter your email to check the status of your professional registration application
            </p>
          </div>

          {/* Search Form */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="applicantType" className="block text-sm font-semibold text-gray-700 mb-2">
                  Application Type
                </label>
                <select
                  id="applicantType"
                  value={applicantType}
                  onChange={(e) => setApplicantType(e.target.value)}
                  className="block w-full px-4 py-3 border-2 rounded-xl text-gray-900 border-gray-200 focus:border-blue-500 focus:ring-blue-500 focus:ring-2 focus:ring-opacity-20"
                >
                  <option value="teacher">Teacher Registration</option>
                  <option value="examiner">Examiner Registration</option>
                </select>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="block w-full px-4 py-3 border-2 rounded-xl text-gray-900 border-gray-200 focus:border-blue-500 focus:ring-blue-500 focus:ring-2 focus:ring-opacity-20"
                  placeholder="Enter your email address"
                />
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    Checking Status...
                  </div>
                ) : (
                  'Check Status'
                )}
              </button>
            </form>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
              <div className="flex items-center">
                <svg className="h-6 w-6 text-red-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-red-800 font-medium">{error}</p>
              </div>
            </div>
          )}

          {/* Application Status Display */}
          {applicationData && (
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
              <div className="text-center mb-6">
                <div className="flex justify-center mb-4">
                  {getStatusIcon(applicationData.status)}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Application Status
                </h3>
                <span className={`inline-flex px-4 py-2 rounded-full text-sm font-semibold ${getStatusColor(applicationData.status)}`}>
                  {applicationData.status.charAt(0).toUpperCase() + applicationData.status.slice(1)}
                </span>
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">Status Message</h4>
                  <p className="text-gray-700">{applicationData.statusMessage}</p>
                </div>

                {applicationData.nextSteps && applicationData.nextSteps.length > 0 && (
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">Next Steps</h4>
                    <ul className="space-y-2">
                      {applicationData.nextSteps.map((step: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-600 mr-3 mt-1">•</span>
                          <span className="text-gray-700">{step}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Application ID</h4>
                    <p className="text-gray-600 font-mono text-sm">{applicationData.applicationId}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Submitted</h4>
                    <p className="text-gray-600">{new Date(applicationData.submittedAt).toLocaleDateString()}</p>
                  </div>
                  {applicationData.reviewedAt && (
                    <div>
                      <h4 className="text-sm font-semibold text-gray-900 mb-2">Reviewed</h4>
                      <p className="text-gray-600">{new Date(applicationData.reviewedAt).toLocaleDateString()}</p>
                    </div>
                  )}
                  {applicationData.approvedAt && (
                    <div>
                      <h4 className="text-sm font-semibold text-gray-900 mb-2">Approved</h4>
                      <p className="text-gray-600">{new Date(applicationData.approvedAt).toLocaleDateString()}</p>
                    </div>
                  )}
                </div>

                {applicationData.estimatedProcessingTime && applicationData.status === 'pending' && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-sm font-semibold text-blue-900 mb-1">Estimated Processing Time</h4>
                    <p className="text-blue-800">{applicationData.estimatedProcessingTime}</p>
                  </div>
                )}

                {applicationData.rejectionReason && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h4 className="text-sm font-semibold text-red-900 mb-2">Rejection Reason</h4>
                    <p className="text-red-800">{applicationData.rejectionReason}</p>
                  </div>
                )}

                {applicationData.canLogin && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <p className="text-green-800 font-medium mb-3">
                      🎉 Your account is ready! You can now log in to the system.
                    </p>
                    <Link
                      href="/auth/Login"
                      className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      Go to Login
                    </Link>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
