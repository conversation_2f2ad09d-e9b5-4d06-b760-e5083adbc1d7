#!/usr/bin/env tsx
/**
 * Database Health Check Script
 * Comprehensive health check for the GCE System database
 */

import { PrismaClient } from '../src/generated/prisma';
import * as fs from 'fs';

const prisma = new PrismaClient();

interface HealthCheckResult {
  timestamp: string;
  database: {
    connection: boolean;
    type: string;
    version?: string;
  };
  schemas: {
    [schema: string]: {
      exists: boolean;
      tables: string[];
      recordCounts: { [table: string]: number };
    };
  };
  performance: {
    connectionTime: number;
    queryTime: number;
  };
  issues: string[];
  recommendations: string[];
}

async function checkDatabaseConnection(): Promise<{ connected: boolean; time: number; version?: string }> {
  const startTime = Date.now();
  
  try {
    await prisma.$connect();
    const result = await prisma.$queryRaw`SELECT version()` as any[];
    const connectionTime = Date.now() - startTime;
    
    return {
      connected: true,
      time: connectionTime,
      version: result[0]?.version || 'Unknown'
    };
  } catch (error) {
    return {
      connected: false,
      time: Date.now() - startTime
    };
  }
}

async function checkSchemaHealth(schemaName: string) {
  const result = {
    exists: false,
    tables: [] as string[],
    recordCounts: {} as { [table: string]: number }
  };

  try {
    // Check if schema exists
    const schemas = await prisma.$queryRaw`
      SELECT schema_name 
      FROM information_schema.schemata 
      WHERE schema_name = ${schemaName}
    ` as any[];
    
    result.exists = schemas.length > 0;

    if (result.exists) {
      // Get tables in schema
      const tables = await prisma.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = ${schemaName}
      ` as any[];
      
      result.tables = tables.map((t: any) => t.table_name);

      // Get record counts for each table
      for (const table of result.tables) {
        try {
          const count = await prisma.$queryRawUnsafe(`
            SELECT COUNT(*) as count 
            FROM "${schemaName}"."${table.table_name}"
          `) as any[];
          result.recordCounts[table.table_name] = parseInt(count[0]?.count || '0');
        } catch (error) {
          result.recordCounts[table.table_name] = -1; // Error getting count
        }
      }
    }
  } catch (error) {
    console.error(`Error checking schema ${schemaName}:`, error);
  }

  return result;
}

async function checkCoreTableData() {
  const issues: string[] = [];
  const recommendations: string[] = [];

  try {
    // Check if core tables have data
    const subjectCount = await prisma.subject.count();
    const schoolCount = await prisma.school.count();
    const examCenterCount = await prisma.examCenter.count();

    if (subjectCount === 0) {
      issues.push('No subjects found in database');
      recommendations.push('Run database seeding: npx prisma db seed');
    }

    if (schoolCount === 0) {
      issues.push('No schools found in database');
      recommendations.push('Add school data through admin interface or seeding');
    }

    if (examCenterCount === 0) {
      issues.push('No exam centers found in database');
      recommendations.push('Add exam center data through admin interface');
    }

    // Check for orphaned records
    const schoolPreRegs = await prisma.schoolPreRegistration.count({
      where: {
        status: 'pending',
        createdAt: {
          lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
        }
      }
    });

    if (schoolPreRegs > 0) {
      issues.push(`${schoolPreRegs} pending school pre-registrations older than 7 days`);
      recommendations.push('Review and clean up old pending registrations');
    }

    // Check for expired invitations
    const expiredInvitations = await prisma.registrationInvitation.count({
      where: {
        expiresAt: {
          lt: new Date()
        },
        isUsed: false
      }
    });

    if (expiredInvitations > 0) {
      issues.push(`${expiredInvitations} expired unused invitations`);
      recommendations.push('Clean up expired invitations');
    }

  } catch (error) {
    issues.push(`Error checking core table data: ${error}`);
  }

  return { issues, recommendations };
}

async function performanceTest() {
  const startTime = Date.now();
  
  try {
    // Test basic queries
    await Promise.all([
      prisma.subject.findMany({ take: 10 }),
      prisma.school.findMany({ take: 10 }),
      prisma.examCenter.findMany({ take: 10 })
    ]);
    
    return Date.now() - startTime;
  } catch (error) {
    return -1;
  }
}

async function runHealthCheck(): Promise<HealthCheckResult> {
  console.log('🏥 Running Database Health Check...\n');

  const result: HealthCheckResult = {
    timestamp: new Date().toISOString(),
    database: {
      connection: false,
      type: 'PostgreSQL'
    },
    schemas: {},
    performance: {
      connectionTime: 0,
      queryTime: 0
    },
    issues: [],
    recommendations: []
  };

  // Check database connection
  console.log('🔍 Checking database connection...');
  const connectionCheck = await checkDatabaseConnection();
  result.database.connection = connectionCheck.connected;
  result.database.version = connectionCheck.version;
  result.performance.connectionTime = connectionCheck.time;

  if (!connectionCheck.connected) {
    result.issues.push('Database connection failed');
    result.recommendations.push('Check database server status and connection string');
    return result;
  }

  console.log('✅ Database connection successful');

  // Check schemas
  console.log('🔍 Checking database schemas...');
  const schemas = ['public', 'o_level_students', 'a_level_students', 'teacher_auth', 'examiner_auth'];
  
  for (const schema of schemas) {
    console.log(`   Checking schema: ${schema}`);
    result.schemas[schema] = await checkSchemaHealth(schema);
    
    if (!result.schemas[schema].exists) {
      result.issues.push(`Schema '${schema}' does not exist`);
      result.recommendations.push(`Create schema '${schema}' or run migrations`);
    }
  }

  // Check core data
  console.log('🔍 Checking core table data...');
  const dataCheck = await checkCoreTableData();
  result.issues.push(...dataCheck.issues);
  result.recommendations.push(...dataCheck.recommendations);

  // Performance test
  console.log('🔍 Running performance test...');
  result.performance.queryTime = await performanceTest();
  
  if (result.performance.queryTime > 1000) {
    result.issues.push('Slow query performance detected');
    result.recommendations.push('Consider database optimization or indexing');
  }

  return result;
}

async function generateHealthReport(result: HealthCheckResult) {
  console.log('\n📊 Database Health Report');
  console.log('========================\n');

  // Connection status
  console.log(`🔗 Connection: ${result.database.connection ? '✅ Connected' : '❌ Failed'}`);
  console.log(`📊 Database: ${result.database.type} ${result.database.version || ''}`);
  console.log(`⏱️  Connection Time: ${result.performance.connectionTime}ms`);
  console.log(`⏱️  Query Time: ${result.performance.queryTime}ms\n`);

  // Schema status
  console.log('📁 Schemas:');
  Object.entries(result.schemas).forEach(([name, schema]) => {
    console.log(`   ${schema.exists ? '✅' : '❌'} ${name}: ${schema.tables.length} tables`);
    if (schema.exists && Object.keys(schema.recordCounts).length > 0) {
      Object.entries(schema.recordCounts).forEach(([table, count]) => {
        console.log(`      ${table}: ${count >= 0 ? count : 'Error'} records`);
      });
    }
  });

  // Issues and recommendations
  if (result.issues.length > 0) {
    console.log('\n⚠️  Issues Found:');
    result.issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
  }

  if (result.recommendations.length > 0) {
    console.log('\n💡 Recommendations:');
    result.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });
  }

  if (result.issues.length === 0) {
    console.log('\n✅ No issues found! Database is healthy.');
  }

  // Save report to file
  const reportPath = `./health-check-${new Date().toISOString().split('T')[0]}.json`;
  fs.writeFileSync(reportPath, JSON.stringify(result, null, 2));
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
}

async function main() {
  try {
    const result = await runHealthCheck();
    await generateHealthReport(result);
  } catch (error) {
    console.error('❌ Health check failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export { runHealthCheck };
