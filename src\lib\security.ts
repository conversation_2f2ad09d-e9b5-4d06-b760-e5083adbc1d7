/**
 * Enhanced Security Library for GCE System
 * Implements comprehensive security measures including rate limiting,
 * input validation, CSRF protection, and security headers
 */

import crypto from 'crypto';
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '../generated/prisma';

const prisma = new PrismaClient();

// Security Configuration
export const SECURITY_CONFIG = {
  // Rate Limiting
  RATE_LIMIT: {
    WINDOW_MS: 15 * 60 * 1000, // 15 minutes
    MAX_REQUESTS: 100,
    MAX_LOGIN_ATTEMPTS: 5,
    MAX_REGISTRATION_ATTEMPTS: 3,
    LOCKOUT_DURATION: 30 * 60 * 1000, // 30 minutes
  },
  
  // Password Policy
  PASSWORD_POLICY: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SPECIAL_CHARS: true,
    BCRYPT_ROUNDS: 12,
  },
  
  // Session Security
  SESSION: {
    MAX_AGE: 24 * 60 * 60 * 1000, // 24 hours
    SECURE_COOKIES: true,
    HTTP_ONLY: true,
    SAME_SITE: 'strict' as const,
  },
  
  // Input Validation
  INPUT_LIMITS: {
    MAX_EMAIL_LENGTH: 254,
    MAX_NAME_LENGTH: 100,
    MAX_TEXT_LENGTH: 1000,
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  },
  
  // CSRF Protection
  CSRF: {
    TOKEN_LENGTH: 32,
    TOKEN_EXPIRY: 60 * 60 * 1000, // 1 hour
  }
};

// Rate Limiting Store (In production, use Redis)
interface RateLimitEntry {
  count: number;
  resetTime: number;
  blocked: boolean;
  blockExpiry?: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

/**
 * Rate Limiting Middleware
 */
export class RateLimiter {
  static getKey(ip: string, endpoint?: string): string {
    return endpoint ? `${ip}:${endpoint}` : ip;
  }

  static async checkRateLimit(
    ip: string, 
    endpoint?: string,
    customLimits?: { windowMs: number; maxRequests: number }
  ): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const key = this.getKey(ip, endpoint);
    const now = Date.now();
    const windowMs = customLimits?.windowMs || SECURITY_CONFIG.RATE_LIMIT.WINDOW_MS;
    const maxRequests = customLimits?.maxRequests || SECURITY_CONFIG.RATE_LIMIT.MAX_REQUESTS;

    let entry = rateLimitStore.get(key);

    // Initialize or reset if window expired
    if (!entry || now > entry.resetTime) {
      entry = {
        count: 0,
        resetTime: now + windowMs,
        blocked: false
      };
    }

    // Check if currently blocked
    if (entry.blocked && entry.blockExpiry && now < entry.blockExpiry) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.blockExpiry
      };
    }

    // Remove block if expired
    if (entry.blocked && entry.blockExpiry && now >= entry.blockExpiry) {
      entry.blocked = false;
      entry.blockExpiry = undefined;
      entry.count = 0;
      entry.resetTime = now + windowMs;
    }

    entry.count++;

    // Block if limit exceeded
    if (entry.count > maxRequests) {
      entry.blocked = true;
      entry.blockExpiry = now + SECURITY_CONFIG.RATE_LIMIT.LOCKOUT_DURATION;
      
      // Log security event
      await this.logSecurityEvent('RATE_LIMIT_EXCEEDED', {
        ip,
        endpoint,
        count: entry.count,
        maxRequests
      });
    }

    rateLimitStore.set(key, entry);

    return {
      allowed: !entry.blocked,
      remaining: Math.max(0, maxRequests - entry.count),
      resetTime: entry.resetTime
    };
  }

  static async logSecurityEvent(event: string, data: any): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          tableName: 'security_events',
          recordId: crypto.randomUUID(),
          action: event,
          newValues: data,
          userType: 'system',
          userId: 'security-system',
          userEmail: '<EMAIL>',
          ipAddress: data.ip || 'unknown',
          timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }
}

/**
 * Input Validation and Sanitization
 */
export class InputValidator {
  static validateEmail(email: string): { valid: boolean; error?: string } {
    if (!email || typeof email !== 'string') {
      return { valid: false, error: 'Email is required' };
    }

    if (email.length > SECURITY_CONFIG.INPUT_LIMITS.MAX_EMAIL_LENGTH) {
      return { valid: false, error: 'Email is too long' };
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { valid: false, error: 'Invalid email format' };
    }

    return { valid: true };
  }

  static validatePassword(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const config = SECURITY_CONFIG.PASSWORD_POLICY;

    if (!password || typeof password !== 'string') {
      errors.push('Password is required');
      return { valid: false, errors };
    }

    if (password.length < config.MIN_LENGTH) {
      errors.push(`Password must be at least ${config.MIN_LENGTH} characters long`);
    }

    if (password.length > config.MAX_LENGTH) {
      errors.push(`Password must be no more than ${config.MAX_LENGTH} characters long`);
    }

    if (config.REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (config.REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (config.REQUIRE_NUMBERS && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (config.REQUIRE_SPECIAL_CHARS && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return { valid: errors.length === 0, errors };
  }

  static sanitizeString(input: string, maxLength?: number): string {
    if (!input || typeof input !== 'string') return '';
    
    // Remove potentially dangerous characters
    let sanitized = input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<[^>]*>/g, '')
      .trim();

    if (maxLength) {
      sanitized = sanitized.substring(0, maxLength);
    }

    return sanitized;
  }

  static validateName(name: string): { valid: boolean; error?: string } {
    if (!name || typeof name !== 'string') {
      return { valid: false, error: 'Name is required' };
    }

    const sanitized = this.sanitizeString(name, SECURITY_CONFIG.INPUT_LIMITS.MAX_NAME_LENGTH);
    
    if (sanitized.length < 2) {
      return { valid: false, error: 'Name must be at least 2 characters long' };
    }

    if (!/^[a-zA-Z\s'-]+$/.test(sanitized)) {
      return { valid: false, error: 'Name contains invalid characters' };
    }

    return { valid: true };
  }

  static validatePhoneNumber(phone: string): { valid: boolean; error?: string } {
    if (!phone || typeof phone !== 'string') {
      return { valid: false, error: 'Phone number is required' };
    }

    // Cameroon phone number format: +237XXXXXXXXX or 6XXXXXXXX
    const phoneRegex = /^(\+237|237)?[6-9]\d{8}$/;
    const cleanPhone = phone.replace(/[\s-()]/g, '');

    if (!phoneRegex.test(cleanPhone)) {
      return { valid: false, error: 'Invalid Cameroon phone number format' };
    }

    return { valid: true };
  }
}

/**
 * CSRF Protection
 */
export class CSRFProtection {
  private static tokens = new Map<string, { token: string; expiry: number; used: boolean }>();

  static generateToken(sessionId: string): string {
    const token = crypto.randomBytes(SECURITY_CONFIG.CSRF.TOKEN_LENGTH).toString('hex');
    const expiry = Date.now() + SECURITY_CONFIG.CSRF.TOKEN_EXPIRY;

    this.tokens.set(sessionId, { token, expiry, used: false });
    return token;
  }

  static validateToken(sessionId: string, providedToken: string): boolean {
    const tokenData = this.tokens.get(sessionId);
    
    if (!tokenData) return false;
    if (tokenData.used) return false;
    if (Date.now() > tokenData.expiry) {
      this.tokens.delete(sessionId);
      return false;
    }
    if (tokenData.token !== providedToken) return false;

    // Mark token as used (one-time use)
    tokenData.used = true;
    return true;
  }

  static cleanupExpiredTokens(): void {
    const now = Date.now();
    for (const [sessionId, tokenData] of this.tokens.entries()) {
      if (now > tokenData.expiry || tokenData.used) {
        this.tokens.delete(sessionId);
      }
    }
  }
}

/**
 * Security Headers
 */
export class SecurityHeaders {
  static apply(response: NextResponse): NextResponse {
    // Content Security Policy
    response.headers.set(
      'Content-Security-Policy',
      "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: https:; " +
      "font-src 'self' data:; " +
      "connect-src 'self'; " +
      "frame-ancestors 'none';"
    );

    // Security headers
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    
    // HSTS (only in production with HTTPS)
    if (process.env.NODE_ENV === 'production') {
      response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    }

    return response;
  }
}

/**
 * Security Middleware Factory
 */
export function createSecurityMiddleware(options: {
  rateLimitEndpoint?: string;
  requireCSRF?: boolean;
  customRateLimit?: { windowMs: number; maxRequests: number };
}) {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown';

    // Apply rate limiting
    const rateLimit = await RateLimiter.checkRateLimit(
      ip, 
      options.rateLimitEndpoint,
      options.customRateLimit
    );

    if (!rateLimit.allowed) {
      const response = NextResponse.json(
        { 
          error: 'Too many requests. Please try again later.',
          retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
        },
        { status: 429 }
      );
      
      response.headers.set('Retry-After', String(Math.ceil((rateLimit.resetTime - Date.now()) / 1000)));
      return SecurityHeaders.apply(response);
    }

    // CSRF Protection for state-changing operations
    if (options.requireCSRF && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {
      const csrfToken = request.headers.get('x-csrf-token');
      const sessionId = request.headers.get('x-session-id');

      if (!csrfToken || !sessionId || !CSRFProtection.validateToken(sessionId, csrfToken)) {
        const response = NextResponse.json(
          { error: 'Invalid CSRF token' },
          { status: 403 }
        );
        return SecurityHeaders.apply(response);
      }
    }

    return null; // Continue to next middleware/handler
  };
}

// Cleanup expired tokens periodically
setInterval(() => {
  CSRFProtection.cleanupExpiredTokens();
}, 5 * 60 * 1000); // Every 5 minutes

export { SECURITY_CONFIG, RateLimiter, InputValidator, CSRFProtection, SecurityHeaders };
