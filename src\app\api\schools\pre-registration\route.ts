import { NextRequest, NextResponse } from 'next/server';
// import { prisma } from '@/lib/postgresDb';
import { generateInvitationCode, logRegistrationAttempt, assessRegistrationRisk } from '@/lib/secureRegistration';

// Mock data storage for testing (in production, use database)
const mockSchools = new Map([
  ['test-school-1', {
    id: 'test-school-1',
    name: 'Test High School Yaoundé',
    centerNumber: '001',
    region: 'Centre',
    createdAt: new Date(),
    updatedAt: new Date()
  }]
]);

const mockPreRegistrations = new Map();

// GET /api/schools/pre-registration - Get pre-registrations for a school
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const schoolId = searchParams.get('schoolId');
    const status = searchParams.get('status');

    if (!schoolId) {
      return NextResponse.json(
        { success: false, message: 'School ID is required' },
        { status: 400 }
      );
    }

    // Build where clause
    const whereClause: any = { schoolId };
    if (status) {
      whereClause.status = status;
    }

    // Use mock data instead of database
    const preRegistrations = Array.from(mockPreRegistrations.values())
      .filter((reg: any) => {
        if (reg.schoolId !== schoolId) return false;
        if (status && reg.status !== status) return false;
        return true;
      })
      .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json({
      success: true,
      data: preRegistrations,
      message: 'Pre-registrations retrieved successfully'
    });

  } catch (error) {
    console.error('Get pre-registrations error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/schools/pre-registration - Create a new pre-registration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      schoolId,
      studentName,
      nationalId,
      dateOfBirth,
      gender,
      examLevel,
      academicYear = '2024/2025',
      parentGuardianName,
      parentGuardianPhone,
      verifiedBy // School admin/teacher creating the pre-registration
    } = body;

    // Get IP and user agent for security logging
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || '';

    // Validate required fields
    if (!studentName || !examLevel) {
      // Skip logging for now since we don't have database
      return NextResponse.json(
        { success: false, message: 'Missing required fields: studentName, examLevel' },
        { status: 400 }
      );
    }

    // Validate exam level
    if (!['O Level', 'A Level'].includes(examLevel)) {
      return NextResponse.json(
        { success: false, message: 'Invalid exam level. Must be "O Level" or "A Level"' },
        { status: 400 }
      );
    }

    // Use default school for testing
    const defaultSchoolId = schoolId || 'test-school-1';
    const school = mockSchools.get(defaultSchoolId);

    if (!school) {
      return NextResponse.json(
        { success: false, message: 'School not found' },
        { status: 404 }
      );
    }

    // Check for existing pre-registration with same details
    if (nationalId) {
      const existingPreReg = Array.from(mockPreRegistrations.values())
        .find((reg: any) =>
          reg.nationalId === nationalId &&
          reg.examLevel === examLevel &&
          ['pending', 'verified', 'invitation_sent'].includes(reg.status)
        );

      if (existingPreReg) {
        return NextResponse.json(
          { success: false, message: 'A pre-registration already exists for this student' },
          { status: 409 }
        );
      }
    }

    // Skip risk assessment for testing
    // const riskAssessment = await assessRegistrationRisk(ipAddress, userAgent, '', nationalId);

    // Create mock pre-registration
    const preRegistrationId = `prereg_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    const preRegistration = {
      id: preRegistrationId,
      schoolId: defaultSchoolId,
      studentName,
      nationalId,
      dateOfBirth,
      gender,
      examLevel,
      academicYear,
      parentGuardianName,
      parentGuardianPhone,
      verifiedBySchool: true,
      verifiedBy: verifiedBy || 'Test Admin',
      verificationDate: new Date(),
      status: 'verified',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Store in mock database
    mockPreRegistrations.set(preRegistrationId, preRegistration);

    return NextResponse.json({
      success: true,
      data: preRegistration,
      message: 'Pre-registration created successfully'
    });

  } catch (error) {
    console.error('Create pre-registration error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/schools/pre-registration - Update pre-registration status
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, status, verifiedBy } = body;

    if (!id || !status) {
      return NextResponse.json(
        { success: false, message: 'Pre-registration ID and status are required' },
        { status: 400 }
      );
    }

    // Validate status
    const validStatuses = ['pending', 'verified', 'invitation_sent', 'completed', 'expired'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, message: 'Invalid status' },
        { status: 400 }
      );
    }

    // Update mock data
    const preRegistration = mockPreRegistrations.get(id);
    if (!preRegistration) {
      return NextResponse.json(
        { success: false, message: 'Pre-registration not found' },
        { status: 404 }
      );
    }

    preRegistration.status = status;
    preRegistration.updatedAt = new Date();

    if (status === 'verified' && verifiedBy) {
      preRegistration.verifiedBySchool = true;
      preRegistration.verifiedBy = verifiedBy;
      preRegistration.verificationDate = new Date();
    }

    mockPreRegistrations.set(id, preRegistration);

    return NextResponse.json({
      success: true,
      data: preRegistration,
      message: 'Pre-registration updated successfully'
    });

  } catch (error) {
    console.error('Update pre-registration error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/schools/pre-registration - Delete a pre-registration
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Pre-registration ID is required' },
        { status: 400 }
      );
    }

    // Check if pre-registration exists and is not completed
    const preRegistration = mockPreRegistrations.get(id);

    if (!preRegistration) {
      return NextResponse.json(
        { success: false, message: 'Pre-registration not found' },
        { status: 404 }
      );
    }

    if (preRegistration.status === 'completed') {
      return NextResponse.json(
        { success: false, message: 'Cannot delete completed pre-registration' },
        { status: 400 }
      );
    }

    mockPreRegistrations.delete(id);

    return NextResponse.json({
      success: true,
      message: 'Pre-registration deleted successfully'
    });

  } catch (error) {
    console.error('Delete pre-registration error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
