// Test profile picture loading
const http = require('http');

function testLogin() {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      email: '<EMAIL>',
      password: 'demo123',
      userType: 'student'
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.write(data);
    req.end();
  });
}

function testStudentAPI(studentId, token) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/students/${studentId}?examLevel=${encodeURIComponent('A Level')}`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.end();
  });
}

function testImageAccess() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/images/prince.jpg',
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      resolve({ status: res.statusCode, headers: res.headers });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.end();
  });
}

async function main() {
  console.log('🖼️ Testing Profile Picture Loading...\n');
  
  try {
    // Test 1: Check if image is accessible
    console.log('1. Testing direct image access...');
    const imageResult = await testImageAccess();
    console.log(`   Status: ${imageResult.status}`);
    console.log(`   Content-Type: ${imageResult.headers['content-type']}`);
    
    if (imageResult.status === 200) {
      console.log('   ✅ Image is accessible at /images/prince.jpg');
    } else {
      console.log('   ❌ Image not accessible - this is the problem!');
    }
    
    // Test 2: Login and get user data
    console.log('\n2. Testing login and user data...');
    const loginResult = await testLogin();
    
    if (loginResult.status === 200 && loginResult.data.success) {
      console.log('   ✅ Login successful');
      
      // Test 3: Get student data with profile picture
      console.log('\n3. Testing student API response...');
      const studentResult = await testStudentAPI(loginResult.data.data.id, loginResult.data.data.token);
      
      if (studentResult.status === 200) {
        const student = studentResult.data.data;
        console.log('   ✅ Student data loaded');
        console.log(`   Profile Picture Path: ${student.profilePicturePath}`);
        console.log(`   Photo URL: ${student.photoUrl}`);
        console.log(`   Has Profile Picture: ${student.hasProfilePicture}`);
        
        if (student.profilePicturePath === '/images/prince.jpg') {
          console.log('   ✅ Profile picture path is correct');
        } else {
          console.log('   ❌ Profile picture path is incorrect');
        }
        
        console.log('\n🎯 DIAGNOSIS:');
        if (imageResult.status === 200 && student.profilePicturePath === '/images/prince.jpg') {
          console.log('   ✅ Everything should be working!');
          console.log('   ✅ Image is accessible');
          console.log('   ✅ Profile path is correct');
          console.log('   ✅ Ready for demo!');
        } else {
          console.log('   ❌ Issues found:');
          if (imageResult.status !== 200) {
            console.log('   - Image not accessible at /images/prince.jpg');
          }
          if (student.profilePicturePath !== '/images/prince.jpg') {
            console.log('   - Profile picture path is incorrect');
          }
        }
        
      } else {
        console.log('   ❌ Student API failed');
        console.log(`   Response:`, studentResult.data);
      }
      
    } else {
      console.log('   ❌ Login failed');
      console.log(`   Response:`, loginResult.data);
    }
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

main();
