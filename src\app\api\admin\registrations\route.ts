import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/postgresDb';
import SeparateStudentDatabase from '@/lib/separateStudentDb';

// GET /api/admin/registrations - Get all pending registrations for admin approval
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'pending';
    const userType = searchParams.get('userType');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    let registrations = [];

    if (!userType || userType === 'student') {
      // Get student registrations from separate database
      const studentRegistrations = await SeparateStudentDatabase.getPendingRegistrations(status, limit, offset);
      registrations = [...registrations, ...studentRegistrations.map(reg => ({
        ...reg,
        userType: 'student',
        registrationType: 'student'
      }))];
    }

    if (!userType || userType === 'professional') {
      // Get professional registrations (teachers/examiners)
      const professionalRegistrations = await prisma.professionalRegistration.findMany({
        where: { status },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
        select: {
          id: true,
          applicantType: true,
          fullName: true,
          email: true,
          currentEmployer: true,
          qualifications: true,
          experience: true,
          status: true,
          submittedAt: true,
          documents: true,
          createdAt: true
        }
      });

      registrations = [...registrations, ...professionalRegistrations.map(reg => ({
        ...reg,
        userType: reg.applicantType,
        registrationType: 'professional'
      }))];
    }

    // Sort by creation date
    registrations.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json({
      success: true,
      data: {
        registrations: registrations.slice(0, limit),
        pagination: {
          page,
          limit,
          total: registrations.length,
          hasMore: registrations.length > limit
        }
      },
      message: 'Registrations retrieved successfully'
    });

  } catch (error) {
    console.error('Get registrations error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/registrations - Approve or reject registration
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      registrationId, 
      registrationType, 
      action, 
      adminId, 
      adminName,
      rejectionReason,
      notes 
    } = body;

    if (!registrationId || !registrationType || !action || !adminId) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (!['approve', 'reject', 'request_documents'].includes(action)) {
      return NextResponse.json(
        { success: false, message: 'Invalid action' },
        { status: 400 }
      );
    }

    let result;

    if (registrationType === 'student') {
      // Handle student registration approval
      result = await handleStudentApproval(registrationId, action, adminId, adminName, rejectionReason, notes);
    } else if (registrationType === 'professional') {
      // Handle professional registration approval
      result = await handleProfessionalApproval(registrationId, action, adminId, adminName, rejectionReason, notes);
    } else {
      return NextResponse.json(
        { success: false, message: 'Invalid registration type' },
        { status: 400 }
      );
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('Registration approval error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle student registration approval
async function handleStudentApproval(
  registrationId: string, 
  action: string, 
  adminId: string, 
  adminName: string,
  rejectionReason?: string,
  notes?: string
) {
  const student = await SeparateStudentDatabase.findStudentById(registrationId);
  
  if (!student) {
    return { success: false, message: 'Student registration not found' };
  }

  if (action === 'approve') {
    // Approve student registration
    await SeparateStudentDatabase.updateStudentStatus(registrationId, 'confirmed', {
      approvedBy: adminId,
      approvedByName: adminName,
      approvedAt: new Date(),
      adminNotes: notes
    });

    // Create audit log
    await prisma.auditLog.create({
      data: {
        tableName: 'student_registrations',
        recordId: registrationId,
        action: 'APPROVED',
        oldValues: { status: 'pending' },
        newValues: { 
          status: 'confirmed',
          approvedBy: adminId,
          approvedByName: adminName
        },
        userType: 'admin',
        userId: adminId,
        userEmail: adminName,
        timestamp: new Date()
      }
    });

    return {
      success: true,
      message: 'Student registration approved successfully'
    };

  } else if (action === 'reject') {
    // Reject student registration
    await SeparateStudentDatabase.updateStudentStatus(registrationId, 'rejected', {
      rejectedBy: adminId,
      rejectedByName: adminName,
      rejectedAt: new Date(),
      rejectionReason,
      adminNotes: notes
    });

    // Create audit log
    await prisma.auditLog.create({
      data: {
        tableName: 'student_registrations',
        recordId: registrationId,
        action: 'REJECTED',
        oldValues: { status: 'pending' },
        newValues: { 
          status: 'rejected',
          rejectedBy: adminId,
          rejectionReason
        },
        userType: 'admin',
        userId: adminId,
        userEmail: adminName,
        timestamp: new Date()
      }
    });

    return {
      success: true,
      message: 'Student registration rejected'
    };

  } else if (action === 'request_documents') {
    // Request additional documents
    await SeparateStudentDatabase.updateStudentStatus(registrationId, 'documents_requested', {
      documentsRequestedBy: adminId,
      documentsRequestedAt: new Date(),
      documentRequest: notes
    });

    return {
      success: true,
      message: 'Additional documents requested'
    };
  }
}

// Handle professional registration approval
async function handleProfessionalApproval(
  registrationId: string, 
  action: string, 
  adminId: string, 
  adminName: string,
  rejectionReason?: string,
  notes?: string
) {
  const professional = await prisma.professionalRegistration.findUnique({
    where: { id: registrationId }
  });

  if (!professional) {
    return { success: false, message: 'Professional registration not found' };
  }

  if (action === 'approve') {
    // Approve professional registration
    const updatedRegistration = await prisma.professionalRegistration.update({
      where: { id: registrationId },
      data: {
        status: 'approved',
        approvedBy: adminName,
        approvedAt: new Date(),
        reviewedBy: adminName,
        reviewedAt: new Date()
      }
    });

    // Create audit log
    await prisma.auditLog.create({
      data: {
        tableName: 'professional_registrations',
        recordId: registrationId,
        action: 'APPROVED',
        oldValues: { status: professional.status },
        newValues: { 
          status: 'approved',
          approvedBy: adminName
        },
        userType: 'admin',
        userId: adminId,
        userEmail: adminName,
        timestamp: new Date()
      }
    });

    return {
      success: true,
      message: `${professional.applicantType} registration approved successfully`,
      data: updatedRegistration
    };

  } else if (action === 'reject') {
    // Reject professional registration
    const updatedRegistration = await prisma.professionalRegistration.update({
      where: { id: registrationId },
      data: {
        status: 'rejected',
        rejectionReason,
        reviewedBy: adminName,
        reviewedAt: new Date()
      }
    });

    // Create audit log
    await prisma.auditLog.create({
      data: {
        tableName: 'professional_registrations',
        recordId: registrationId,
        action: 'REJECTED',
        oldValues: { status: professional.status },
        newValues: { 
          status: 'rejected',
          rejectionReason
        },
        userType: 'admin',
        userId: adminId,
        userEmail: adminName,
        timestamp: new Date()
      }
    });

    return {
      success: true,
      message: `${professional.applicantType} registration rejected`,
      data: updatedRegistration
    };
  }
}

// POST /api/admin/registrations - Bulk approve/reject registrations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      registrationIds, 
      action, 
      adminId, 
      adminName,
      rejectionReason 
    } = body;

    if (!registrationIds || !Array.isArray(registrationIds) || !action || !adminId) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    const results = [];

    for (const regId of registrationIds) {
      try {
        // Determine registration type by checking both databases
        let registrationType = 'student';
        
        const professional = await prisma.professionalRegistration.findUnique({
          where: { id: regId }
        });

        if (professional) {
          registrationType = 'professional';
        }

        let result;
        if (registrationType === 'student') {
          result = await handleStudentApproval(regId, action, adminId, adminName, rejectionReason);
        } else {
          result = await handleProfessionalApproval(regId, action, adminId, adminName, rejectionReason);
        }

        results.push({
          registrationId: regId,
          success: result.success,
          message: result.message
        });

      } catch (error) {
        results.push({
          registrationId: regId,
          success: false,
          message: 'Failed to process registration'
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    return NextResponse.json({
      success: true,
      data: {
        results,
        summary: {
          total: results.length,
          successful: successCount,
          failed: failureCount
        }
      },
      message: `Bulk operation completed: ${successCount} successful, ${failureCount} failed`
    });

  } catch (error) {
    console.error('Bulk registration processing error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
